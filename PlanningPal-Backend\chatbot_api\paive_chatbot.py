import os
import json
import hashlib
import sys
from pathlib import Path
import boto3  # type: ignore

from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.document_loaders import PyPDFLoader
from langchain_aws import BedrockEmbeddings, BedrockLLM  # type: ignore
from langchain_qdrant import QdrantVectorStore  # type: ignore
from langchain.chains import Conversational<PERSON><PERSON><PERSON><PERSON><PERSON>hai<PERSON>
from langchain.prompts import PromptTemplate

from qdrant_client import QdrantClient  # type: ignore
from qdrant_client.http import models  # type: ignore

from dotenv import load_dotenv
import os

load_dotenv()

from fuzzywuzzy import fuzz  
import json
from qdrant_client.models import PointStruct

AWS_REGION = os.getenv("AWS_REGION", "us-east-1")
AWS_ACCESS_KEY = os.getenv("AWS_ACCESS_KEY")
AWS_SECRET_KEY = os.getenv("AWS_SECRET_KEY")
BASE_DIR = os.path.dirname(__file__)
QDRANT_URL = os.getenv("QDRANT_URL")
QDRANT_API_KEY = os.getenv("QDRANT_API_KEY")


DOCS_PATH = os.path.join(BASE_DIR, "Primary_Documents")
# QDRANT_PATH = os.path.join(BASE_DIR, "qdrant_db")
COLLECTION_NAME = "primary_docs_kb"
METADATA_FILE = os.path.join(BASE_DIR, "embedded_metadata.json")

# def clear_qdrant_lock():
#     """Remove Qdrant lock file if it exists"""
#     try: QDRANT_CLIENT.close()
#     except: pass
    
#     lock_file = os.path.join(QDRANT_PATH, ".lock")
#     if os.path.exists(lock_file):
#         try:
#             os.remove(lock_file)
#             print(f"Removed existing lock file: {lock_file}")
#         except Exception as e:
#             print(f"Could not remove lock file: {e}")

# Global instances
BEDROCK_CLIENT = boto3.client(
    "bedrock-runtime", 
    region_name=AWS_REGION,
    aws_access_key_id=AWS_ACCESS_KEY, 
    aws_secret_access_key=AWS_SECRET_KEY
)

# clear_qdrant_lock()
# QDRANT_CLIENT = QdrantClient(path=QDRANT_PATH)
QDRANT_CLIENT = QdrantClient(
    url=QDRANT_URL,
    api_key=QDRANT_API_KEY,
)

EMBEDDINGS = BedrockEmbeddings(
    model_id="amazon.titan-embed-text-v1",
    client=BEDROCK_CLIENT
)

CORRECTION_LLM = BedrockLLM(
    model_id="meta.llama3-70b-instruct-v1:0",
    client=BEDROCK_CLIENT,
    model_kwargs={'max_gen_len': 100, 'temperature': 0.0}
)

SENTIMENT_LLM = BedrockLLM(
    model_id="meta.llama3-70b-instruct-v1:0",
    client=BEDROCK_CLIENT,
    model_kwargs={'max_gen_len': 10, 'temperature': 0.0}
)

MAIN_LLM = BedrockLLM(
    model_id="meta.llama3-70b-instruct-v1:0",
    client=BEDROCK_CLIENT,
    model_kwargs={'max_gen_len': 512, 'temperature': 0.1}
)

QA_PROMPT = """
You are a helpful assistant for UK planning applications. Use the following {context} derived from official offline application
forms and guidance—to answer the user’s question.

Your goal is to help applicants in the Paive online portal (built to manage every step of a planning application, similar to
the UK Planning Portal) understand and complete their tasks.

Answer the question using only
the information provided in the
<context>. If the context is not
enough, fall back to general UK
planning knowledge.

Rules for every response: 
• Be precise, accurate, and
  strictly grounded in the
  context or accepted UK
  planning practice.
• Never fabricate details.
• Keep the entire answer under
  50 words.
• Format for narrow vertical
  display: never exceed 35‑40
  characters per line; insert
  manual line breaks (\n) after
  35‑40 characters; break every
  sentence into multiple short
  lines; leave a blank line
  between paragraphs.
• Lists:
  — Place each bullet (•) on its
    own line.
  — Keep each bullet ≤ 40 chars.
  — Add extra spacing between
    bullets.
• Steps:
  — Put each numbered step on its
    own line following the same
    width rule.

Special cases: 
If the question is ONLY a greeting (e.g. "hi", "hello") with no other content, respond with:
  "Hi, I'm here to assist you\n
   with UK planning applications\n
   on the Paive portal."

For all other questions, do NOT include this greeting.

Insufficient context:
  "I'm sorry, I don't have enough\n
   information to answer that\n
   question right now.\n\n
   I recommend:\n\n
   • Visiting the GOV.UK at\n
   https://www.gov.uk/guidance/making-an-application\n
   They can provide the most\n
   accurate guidance."

Based on user's sentiment, generate a statement that acknowledges the user's sentiment and provides a 
response that is appropriate for the sentiment generated from knowledge base.

User sentiment: {sentiment} 

<context>
{context}
</context>

Question: {question}

Assistant:
"""

def correct_typos(text: str) -> str:
    correction_prompt = (
        f"Correct any typos or misspellings in this sentence:\n\n'{text}'\n\n"
        "Return only the corrected sentence without any explanation."
    )
    corrected_text = CORRECTION_LLM.invoke(correction_prompt).strip()
    return corrected_text if corrected_text else text

def extract_document_metadata(pdf_path):
    return {
        "council": "Newark and Sherwood Council",
        "category": "House holder planning application",
        "title": Path(pdf_path).stem,
        "hash": md5_file(str(pdf_path.resolve()))
    }

def load_faqs_to_qdrant():
    """Load FAQs into Qdrant as payloads"""
    
    # Load FAQ JSON file
    faq_file = os.path.join(BASE_DIR, "FAQs.json")
    
    if not os.path.exists(faq_file):
        print("faqs.json not found, skipping FAQ loading")
        return
    
    with open(faq_file, 'r', encoding='utf-8') as f:
        faqs = json.load(f)
    
    # Create FAQ collection if it doesn't exist
    FAQ_COLLECTION = "faqs_collection"
    # client = QDRANT_CLIENT
    
    try:
        QDRANT_CLIENT.get_collection(collection_name=FAQ_COLLECTION)
        print("FAQ collection already exists")
    except:
        QDRANT_CLIENT.create_collection(
            collection_name=FAQ_COLLECTION,
            vectors_config=models.VectorParams(size=1, distance=models.Distance.DOT)
        )
        print("Created new FAQ collection")
    
    
    # Upload FAQs as payloads
    points = []
    for i, faq in enumerate(faqs):
        points.append(PointStruct(
            id=i,
            vector=[0.0],
            payload= {
                "question": faq["question"],
                "answer": faq["answer"],
                "category": faq["metadata"]["category"],
                "tags": faq["metadata"]["tags"],
                "faq_id": faq["id"]
            }
        ))
    
    QDRANT_CLIENT.upsert(collection_name=FAQ_COLLECTION, points=points)
    print(f"Loaded {len(faqs)} FAQs to Qdrant")

def search_faqs(query, threshold=70):
    """Search FAQs using fuzzy matching"""
    FAQ_COLLECTION = "faqs_collection"
    
    try:
        # Get all FAQ points
        result = QDRANT_CLIENT.scroll(
            collection_name=FAQ_COLLECTION,
            limit=100,
            with_payload=True
        )
        
        best_match = None
        best_score = 0
        
        for point in result[0]:
            faq_question = point.payload["question"]
            
            # Use fuzzy matching
            score = fuzz.ratio(query.lower(), faq_question.lower())
            
            if score > threshold and score > best_score:
                best_score = score
                best_match = point.payload
        
        return best_match, best_score
    
    except Exception as e:
        print(f"Error searching FAQs: {e}")
        return None, 0

def create_kb():
    """Create knowledge base from scratch - loads all PDFs and embeds them"""
    print("Creating knowledge base from scratch...")
    
    # Get all PDF files
    all_pdfs = list(Path(DOCS_PATH).rglob("*.pdf"))
    if not all_pdfs:
        print("No PDFs found in data folder.")
        return QDRANT_CLIENT
    
    print(f"Found {len(all_pdfs)} PDF files to process")
    
    # Delete existing collection if it exists
    if COLLECTION_NAME in [c.name for c in QDRANT_CLIENT.get_collections().collections]:
        print(f"Deleting existing collection: {COLLECTION_NAME}")
        QDRANT_CLIENT.delete_collection(COLLECTION_NAME)
    
    # Create new collection
    print(f"Creating new collection: {COLLECTION_NAME}")
    QDRANT_CLIENT.create_collection(
        collection_name=COLLECTION_NAME,
        vectors_config=models.VectorParams(size=1536, distance=models.Distance.COSINE)
    )
    
    # Embed all PDFs
    embed_new_pdfs(all_pdfs)
    
    # Save metadata for all PDFs
    print("Saving metadata to JSON file...")
    embedded_info = {}
    for pdf_path in all_pdfs:
        pdf_str = str(pdf_path.resolve())
        embedded_info[pdf_str] = extract_document_metadata(pdf_path)
    
    # Ensure directory exists for metadata file
    os.makedirs(os.path.dirname(METADATA_FILE), exist_ok=True)
    
    with open(METADATA_FILE, "w", encoding="utf-8") as f:
        json.dump(embedded_info, f, indent=2)
    
    print(f"Knowledge base creation complete. Processed {len(all_pdfs)} PDFs.")
    return QDRANT_CLIENT

def setup_qdrant_kb():
    try:
        collections = QDRANT_CLIENT.get_collections().collections
        collection_names = [c.name for c in collections]
        
        if COLLECTION_NAME not in collection_names:
            print("Collection not found, creating new knowledge base...")
            return create_kb()
            
        collection_info = QDRANT_CLIENT.get_collection(COLLECTION_NAME)
        if collection_info.points_count == 0:
            print("Collection is empty, creating new knowledge base...")
            return create_kb()
            
        print(f"Using existing knowledge base with {collection_info.points_count} documents...")
        return QDRANT_CLIENT
    except Exception as e:
        print(f"Error accessing knowledge base: {e}")
        print("Creating new knowledge base...")
        return create_kb()

def create_vectorstore():
    return QdrantVectorStore(client=QDRANT_CLIENT, collection_name=COLLECTION_NAME, embedding=EMBEDDINGS)

def initialize_llm(vectorstore):
    prompt = PromptTemplate(
        template=QA_PROMPT, 
        input_variables=["context", "question", "sentiment"]
    )
    
    return ConversationalRetrievalChain.from_llm(
        llm=MAIN_LLM,
        retriever=vectorstore.as_retriever(search_type="similarity", search_kwargs={"k": 3}),
        return_source_documents=True,
        combine_docs_chain_kwargs={"prompt": prompt}
    )

def detect_sentiment(text):
    sentiment_prompt = f"What is the sentiment of this message: '{text}'?\nRespond with one of: Positive, Neutral, Negative."
    return SENTIMENT_LLM.invoke(sentiment_prompt).strip()

def handle_query(query, qa_chain, chat_history=None):
    print(f"DEBUG: Query received: {query}")
        # Test vector search directly
    try:
        vectorstore = create_vectorstore()
        docs = vectorstore.similarity_search(query, k=3)
        print(f"DEBUG: Found {len(docs)} similar documents")
        for i, doc in enumerate(docs):
            print(f"DEBUG: Doc {i+1}: {doc.page_content[:100]}...")
    except Exception as e:
        print(f"DEBUG: Vector search error: {e}")
    if chat_history is None:
        chat_history = []

    corrected_query = correct_typos(query)
    sentiment = detect_sentiment(corrected_query)
    faq_match, faq_score = search_faqs(corrected_query) ## would check faqs first

    formatted_chat_history = []
    for i in range(0, len(chat_history), 2):
        if i + 1 < len(chat_history):
            user_msg = chat_history[i]
            assistant_msg = chat_history[i + 1]
            if user_msg.get("role") == "user" and assistant_msg.get("role") == "assistant":
                formatted_chat_history.append((user_msg.get("content", ""), assistant_msg.get("content", "")))
    
    if faq_match and faq_score > 70:
        answer = faq_match["answer"]
        print(f"FAQ Match found (score: {faq_score}):{faq_match['question']}")
    else:
              # Fall back to regular RAG
              # Convert the dictionary format to tuple format for the QA chain

              # Get response from QA chain
          result = qa_chain.invoke({
        "question": corrected_query,
        "chat_history": formatted_chat_history,
        "sentiment": sentiment
         }) 

    # Extract answer from result
          if isinstance(result, dict):
            answer = result.get("answer", "")
            if not answer:
                answer = result.get("content", "")
                if not answer:
                    answer = str(result)
          else:
              answer = str(result)

    # Update chat history with new exchange
    new_chat_history = chat_history.copy()  # Create a copy to avoid modifying the original
    new_chat_history.append({"role": "user", "content": corrected_query})
    new_chat_history.append({"role": "assistant", "content": answer})

    return answer, new_chat_history

def incremental_reindex():
    """Check for new/changed PDFs and reindex only those"""
    print("Starting incremental reindex...")
    
    if os.path.isfile(METADATA_FILE):
        with open(METADATA_FILE, "r", encoding="utf-8") as f:
            embedded_info = json.load(f)
    else:
        embedded_info = {}

    all_pdfs = list(Path(DOCS_PATH).rglob("*.pdf"))
    if not all_pdfs:
        return {"message": "No PDFs found in data folder.", "updated_pdfs": []}

    new_or_changed_pdfs = []
    for pdf_path in all_pdfs:
        pdf_str = str(pdf_path.resolve())
        current_hash = md5_file(pdf_str)
        old_metadata = embedded_info.get(pdf_str, {})
        old_hash = old_metadata.get("hash")
        if current_hash != old_hash:
            new_or_changed_pdfs.append(pdf_path)

    if not new_or_changed_pdfs:
        return {"message": "No new or changed PDFs. Already up to date.", "updated_pdfs": []}

    # Ensure collection exists
    if COLLECTION_NAME not in [c.name for c in QDRANT_CLIENT.get_collections().collections]:
        QDRANT_CLIENT.create_collection(
            collection_name=COLLECTION_NAME,
            vectors_config=models.VectorParams(size=1536, distance=models.Distance.COSINE)
        )
    
    print(f"Starting to embed {len(new_or_changed_pdfs)} new/changed PDFs...")
    embed_new_pdfs(new_or_changed_pdfs)
    print("Finished embedding PDFs, updating metadata")

    # Update metadata for new/changed PDFs
    for pdf in new_or_changed_pdfs:
        pdf_str = str(pdf.resolve())
        embedded_info[pdf_str] = extract_document_metadata(pdf)

    with open(METADATA_FILE, "w", encoding="utf-8") as f:
        json.dump(embedded_info, f, indent=2)

    return {
        "message": "Reindex complete. Embedded new/changed PDFs.",
        "updated_pdfs": [str(x) for x in new_or_changed_pdfs]
    }

def embed_new_pdfs(pdf_paths):
    """Embed a list of PDF files into the vector store"""
    docs = []
    BATCH_SIZE = 5
    
    print(f"Loading {len(pdf_paths)} PDF files...")
    
    for i in range(0, len(pdf_paths), BATCH_SIZE):
        batch = pdf_paths[i:i + BATCH_SIZE]
        for pdf_file in batch:
            try:
                print(f"Loading PDF: {pdf_file.name}")
                loader = PyPDFLoader(str(pdf_file))
                loaded_docs = loader.load()
                
                # Add metadata to each document
                metadata = extract_document_metadata(pdf_file)
                for doc in loaded_docs:
                    doc.metadata.update(metadata)
                
                docs.extend(loaded_docs)
                
            except Exception as e:
                print(f"Error processing {pdf_file}: {e}")
                continue

    if not docs:
        print("No documents were successfully loaded")
        return

    print(f"Splitting {len(docs)} documents into chunks...")
    
    # Split documents into chunks
    text_splitter = RecursiveCharacterTextSplitter(
        chunk_size=2000,
        chunk_overlap=200,
        length_function=len,
        separators=["\n\n", "\n", " ", ""]
    )
    split_docs = text_splitter.split_documents(docs)
    
    print(f"Created {len(split_docs)} document chunks")

    # Create vector store instance
    vector_store = QdrantVectorStore(
        client=QDRANT_CLIENT,
        collection_name=COLLECTION_NAME,
        embedding=EMBEDDINGS
    )

    # Upload documents in batches
    BATCH_UPLOAD_SIZE = 100
    total_batches = (len(split_docs) + BATCH_UPLOAD_SIZE - 1) // BATCH_UPLOAD_SIZE
    
    for i in range(0, len(split_docs), BATCH_UPLOAD_SIZE):
        batch = split_docs[i:i + BATCH_UPLOAD_SIZE]
        batch_num = i // BATCH_UPLOAD_SIZE + 1
        
        print(f"Uploading batch {batch_num} of {total_batches} ({len(batch)} chunks)")
        try:
            vector_store.add_documents(batch)
        except Exception as e:
            print(f"Error uploading batch {batch_num}: {e}")
            continue
    
    print("Document embedding complete!")

def md5_file(file_path: str) -> str:
    h = hashlib.md5()
    with open(file_path, "rb") as f:
        for chunk in iter(lambda: f.read(4096), b""):
            h.update(chunk)
    return h.hexdigest()

# if __name__ == "__main__":
#     qdrant_client = setup_qdrant_kb()
#     load_faqs_to_qdrant()
#     vectorstore = create_vectorstore()
#     qa_chain = initialize_llm(vectorstore)
#     print("Knowledge base setup complete!")
#     QDRANT_CLIENT.close()