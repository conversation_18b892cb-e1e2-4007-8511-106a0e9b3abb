
import os
import boto3 # type: ignore
from fastapi import FastAPI
from pydantic import BaseModel
import sys
from qdrant_client import QdrantClient # type: ignore

# from .rag_local import (
#     setup_qdrant_kb,
#     create_vectorstore,
#     initialize_llm,
#     handle_query,
#     incremental_reindex,
#     load_faqs_to_qdrant
# )
from .testing import (
    setup_qdrant_kb,
    create_vectorstore,
    initialize_llm,
    handle_query,
    incremental_reindex,
    load_faqs_to_qdrant,
    QDRANT_CLIENT

)
from langchain_qdrant import Qdrant # type: ignore
from qdrant_client import QdrantClient # type: ignore
from qdrant_client.http import models # type: ignore
from langchain_community.embeddings import BedrockEmbeddings
from database_code.database import engine, Base, SessionLocal
from database_code.models import ChatRecord

Base.metadata.create_all(bind=engine)

app = FastAPI(title="RAG Chatbot API", version="1.0.0")

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# AWS SES setup
AWS_REGION = os.getenv("AWS_REGION", "us-east-1")
SMTP_SENDER_EMAIL = os.getenv("SMTP_SENDER_EMAIL")
AWS_ACCESS_KEY = os.getenv('AWS_ACCESS_KEY')
AWS_SECRET_KEY = os.getenv('AWS_SECRET_KEY')
BASE_DIR = os.path.dirname(__file__)
# QDRANT_PATH = os.path.join(BASE_DIR, "qdrant_db")

# Re-init bedrock embeddings if needed
bedrock = boto3.client(service_name="bedrock-runtime",region_name=AWS_REGION,aws_access_key_id=os.getenv("AWS_ACCESS_KEY"),
    aws_secret_access_key=os.getenv("AWS_SECRET_KEY"))
bedrock_embeddings = BedrockEmbeddings(model_id="amazon.titan-embed-text-v1", client=bedrock)


# ---------------------------------------------------------
# 4) In-memory Chat History (mimicking Flask session)
# ---------------------------------------------------------
chat_history = [
    {"role": "assistant", "content": "Hi, I'm here to assist you with\nhouseholder planning applications\nfor Newark and Sherwood District\nCouncil. How can I help you?"     
}
]

# ---------------------------------------------------------
# 5) Pydantic Models
# ---------------------------------------------------------
class UserQuery(BaseModel):
    question: str

class FeedbackRequest(BaseModel):
    record_id: int
    feedback: str  # "yes" or "no"

# Optional: Endpoint to re-ingest PDFs in "data" folder and update FAISS index
@app.post("/reindex")
def reindex_data():

    """
    Calls the incremental_reindex() function from rag_local,
    which only re-embeds new/updated PDFs and append to qdrant database.
    Also loads FAQs into Qdrant.
    """
    print("API endpoint: Starting reindex")

    # Reindex PDFs
    result = incremental_reindex()

    # Load FAQs
    print("API endpoint: Loading FAQs to Qdrant")
    load_faqs_to_qdrant()

    print(f"API endpoint: Reindex completed")
    return result

# Endpoint to ask any question
@app.post("/ask")
def ask_question(payload: UserQuery):
    global chat_history
    
    query = payload.question.strip()
    if not query:
        return {"error": "Question cannot be empty."}
    
    # Setup QA chain
    # client = QdrantClient(path=QDRANT_PATH)
    vectorstore = create_vectorstore()
    qa_chain = initialize_llm(vectorstore)
    
    # Get response
    answer_text, updated_history = handle_query(query, qa_chain, chat_history)

    # Store in database
    db_session = SessionLocal()
    try:
        new_record = ChatRecord(question=query, answer=answer_text, feedback=None)
        db_session.add(new_record)
        db_session.commit()
        record_id = new_record.id
    finally:
        db_session.close()
    
    # Update global chat history
    chat_history = updated_history

    return {
        "chatHistory": chat_history,
        "recordId": record_id
    }
# @app.post("/ask")
# def ask_question(payload: UserQuery):
#     """
#     Receives any user-typed question, runs the RAG pipeline,
#     saves Q&A to DB, updates in-memory chat, and returns JSON.
#     """
#     query = payload.question.strip()
#     if not query:
#         return {"error": "Question cannot be empty."}
    
    
#     client = QdrantClient(path=QDRANT_PATH)
#     vectorstore = create_vectorstore(client)
#     qa_chain = initialize_llm(vectorstore)
#     answer_text, updated_history = handle_query(query, qa_chain, chat_history)

#     # Store the record in MySQL
#     db_session = SessionLocal()
#     try:
#         new_record = ChatRecord(question=query, answer=answer_text, feedback=None)
#         db_session.add(new_record)
#         db_session.commit()
#         record_id = new_record.id
#     finally:
#         db_session.close()
    
#     chat_history[:] = [
#         {"role": "user", "content": q} if i % 2 == 0 else {"role": "assistant", "content": a}
#         for i, (q, a) in enumerate(updated_history)
#     ]

#     # # Update in-memory chat
#     # chat_history.append({"role": "user", "content": query})
#     # chat_history.append({"role": "assistant", "content": answer_text})

#     return {
#         "chatHistory": chat_history,
#         "recordId": record_id
#     }
    

@app.post("/feedback")
def submit_feedback(data: FeedbackRequest):
    """
    Accepts feedback for a specific record in the DB, e.g.:
      {
        "record_id": 1,
        "feedback": "yes"
      }
    """
    if data.feedback.lower() not in ["yes", "no"]:
        return {"error": "Feedback must be 'yes' or 'no'."}

    db_session = SessionLocal()
    try:
        record = db_session.query(ChatRecord).filter_by(id=data.record_id).first()
        if not record:
            return {"error": "Record not found."}

        record.feedback = data.feedback.lower()
        db_session.commit()

        return {
            "message": "Feedback recorded.",
            "record_id": record.id,
            "feedback": record.feedback
        }
    finally:
        db_session.close()

@app.post("/clear_chat")
def clear_chat():
    """
    Resets the in-memory conversation only (does not affect DB).
    """
    global chat_history
    chat_history = [
        {"role": "assistant", "content": "Hi, how can I help you?"}
    ]
    return {
        "success": True,
        "chatHistory": chat_history
    }

