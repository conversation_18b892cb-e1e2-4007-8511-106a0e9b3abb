from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from dotenv import load_dotenv
import os

# Load environment variables from .env file
load_dotenv()

# Load environment variables
RDS_HOST = os.getenv("RDS_HOST")
RDS_PORT = os.getenv("RDS_PORT")
DB_NAME = os.getenv("DB_NAME")
DB_USER = os.getenv("DB_USER")
DB_PASSWORD = os.getenv("DB_PASSWORD")

# Get the database URL from environment variables
DATABASE_URL = f"mysql+pymysql://{DB_USER}:{DB_PASSWORD}@{RDS_HOST}:{RDS_PORT}/{DB_NAME}"

# Create SQLAlchemy engine
engine = create_engine(DATABASE_URL, pool_size=10, max_overflow=20, pool_pre_ping=True)

# Define Base Class for ORM Models
Base = declarative_base()

# Create a SessionLocal class to use in database interactions
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
