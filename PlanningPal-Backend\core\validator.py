import os
import tempfile
from datetime import datetime
from utils.orientation import analyze_pdf
from utils import ocr_extraction, segmentation
from core.config import s3_client, S3_BUCKET_NAME
from core.checklist import evaluate_drawing_presence
import cv2
import uuid
from typing import List, Dict, Any
import shutil

def validate(file_key: str):
    """
    Validates a document by running all validation steps in sequence:
    1. North arrow detection
    2. OCR text extraction (scale, paper size, drawing number)
    3. Plan segmentation and checklist validation
    
    Args:
        file_key (str): The S3 key of the PDF file to validate
    """
    try:
        temp_dir = tempfile.mkdtemp()
       
        # Download the file from S3
        local_path = os.path.join(temp_dir, os.path.basename(file_key))
        print(file_key)
       
        s3_client.download_file(S3_BUCKET_NAME, file_key, local_path)
        print(local_path)
       
        results = {
            "filename": os.path.basename(local_path),
            "timestamp": datetime.utcnow().isoformat(),
            "status": "success"
        }
 
        # 1. North Arrow Detection
        try:
            north_analysis = analyze_pdf(local_path)
            results["north_arrow"] = {
                "found": len(north_analysis["indicators"]) > 0,
                "details": north_analysis
            }
        except Exception as e:
            print(f"Error in north arrow detection: {str(e)}")
            results["north_arrow"] = {
                "found": False,
                "details": {"error": str(e)}
            }
 
        # 2. OCR Text Extraction
        try:
            extractor = ocr_extraction.ArchitecturalPlanExtractor(use_gpu=True, debug=True)
            ocr_results = extractor.extract_info_from_pdf(local_path)
            results["ocr"] = {
                "scale": ocr_results.get("scale"),
                "paper_size": ocr_results.get("paper_size"),
                "drawing_number": ocr_results.get("drawing_number")
            }
        except Exception as e:
            print(f"Error in OCR extraction: {str(e)}")
            results["ocr"] = {
                "scale": None,
                "paper_size": None,
                "drawing_number": None,
                "error": str(e)
            }
 
        # 3. Plan Segmentation
        try:
            segments, annotated_images, labels = segmentation.extract_images_from_pdf(file_key)
            
            if not segments:
                results["segmentation"] = {
                    "success": False,
                    "segments": [],
                    "error": "Failed to segment plans"
                }
            else:
                # Convert segments to match existing format
                serializable_segments = []
                for segment in segments:
                    serializable_segments.append({
                        "x": 0,  
                        "y": 0,
                        "width": 0,
                        "height": 0,
                        "ocr_text": segment.get('text', ''),
                        "plan_type": segment.get('label', 'unknown'),
                        "segment_key": segment.get('segment_key', '')
                    })
               
                results["segmentation"] = {
                    "success": True,
                    "segments": serializable_segments,
                    "annotated_images": annotated_images
                }
        except Exception as e:
            print(f"Error in segmentation: {str(e)}")
            results["segmentation"] = {
                "success": False,
                "segments": [],
                "error": str(e)
            }
 
        # Collect features for checklist evaluation
        features_detected = {
            "north_arrow": results["north_arrow"]["found"],
            "scale": results["ocr"].get("scale"),
            "paper_size": results["ocr"].get("paper_size"),
            "drawing_number": results["ocr"].get("drawing_number"),
            "floor_plans": {
                label.lower(): True for label in labels  
            }
        }
 
        # Evaluate checklist
        try:
            checklist_results = evaluate_drawing_presence(features_detected)
            results["checklist"] = checklist_results
            results["score"] = checklist_results["total_score"]
        except Exception as e:
            print(f"Error in checklist evaluation: {str(e)}")
            results["checklist"] = {"error": str(e)}
            results["score"] = 0.0
 
        # Clean up temporary files
        try:
            if os.path.exists(local_path):
                os.remove(local_path)
            os.rmdir(temp_dir)
        except Exception as e:
            print(f"Error cleaning up temporary files: {str(e)}")
 
        return results
 
    except Exception as e:
        print(f"Error in validation: {str(e)}")
        return {
            "score": 0.0,
            "details": {
                "status": "error",
                "message": str(e),
                "timestamp": datetime.utcnow().isoformat()
            },
            "status": "error",
            "message": "Validation failed",
            "timestamp": datetime.utcnow().isoformat()
        }

def validate_multiple(file_keys: List[str]) -> Dict[str, Any]:
    """
    Validates multiple documents with individual OCR/orientation analysis but combined segmentation.
    
    Args:
        file_keys (List[str]): List of S3 keys of the PDF files to validate
    """
    try:
        temp_dir = tempfile.mkdtemp()
        
        # Initialize results dictionary
        individual_validations = []
        
        # Initialize OCR extractor
        extractor = ocr_extraction.ArchitecturalPlanExtractor(use_gpu=True)
        
        # Process each file individually for OCR and orientation
        for file_key in file_keys:
            individual_result = {
                "file_key": file_key,
                "filename": os.path.basename(file_key),
                "issues": [],
                "ocr_data": None,
                "orientation_data": None,
                "score": 0,
                "checklist": {}
            }
            
            try:
                # Download the file from S3
                local_path = os.path.join(temp_dir, os.path.basename(file_key))
                s3_client.download_file(S3_BUCKET_NAME, file_key, local_path)
                
                # Perform OCR analysis
                try:
                    ocr_data = extractor.extract_info_from_pdf(local_path)
                    if "error" in ocr_data:
                        individual_result["issues"].append(f"Error in OCR extraction: {ocr_data['error']}")
                    else:
                        individual_result["ocr_data"] = {
                            "scale": ocr_data.get("scale"),
                            "paper_size": ocr_data.get("paper_size"),
                            "drawing_number": ocr_data.get("drawing_number")
                        }
                except Exception as e:
                    individual_result["issues"].append(f"Error in OCR extraction: {str(e)}")
                
                # Perform orientation analysis
                try:
                    orientation_data = analyze_pdf(local_path)
                    found = len(orientation_data.get("indicators", [])) > 0
                    individual_result["orientation_data"] = {
                        "found": found,
                        "details": orientation_data
                    }
                except Exception as e:
                    individual_result["issues"].append(f"Error in orientation analysis: {str(e)}")
                    found = False

                # Checklist scoring
                features_detected = {
                    "north_arrow": found,
                    "scale": individual_result["ocr_data"]["scale"] if individual_result["ocr_data"] else None,
                    "paper_size": individual_result["ocr_data"]["paper_size"] if individual_result["ocr_data"] else None,
                    "drawing_number": individual_result["ocr_data"]["drawing_number"] if individual_result["ocr_data"] else None,
                    "floor_plans": {}  
                }
                checklist_results = evaluate_drawing_presence(features_detected)
                individual_result["score"] = checklist_results["total_score"]
                individual_result["checklist"] = checklist_results

            except Exception as e:
                individual_result["issues"].append(f"Error downloading file: {str(e)}")
        
            individual_validations.append(individual_result)
        
        # Perform collective segmentation on all files
        try:
            segmentation_result = segmentation.process_multiple_pdfs(file_keys)
            collective_segmentation = {
                "validation_report": {
                    "processed_files": len(file_keys),
                    "checklist_validation": segmentation_result.get("checklist_validation", {})
                }
            }
        except Exception as e:
            collective_segmentation = {
                "validation_report": {
                    "processed_files": len(file_keys),
                    "checklist_validation": {key: "❌ ERROR" for key in segmentation.CHECK.keys()}
                }
            }
        
        # After processing all files
        total_score = sum(doc.get("score", 0) for doc in individual_validations)
        average_score = total_score / len(individual_validations) if individual_validations else 0

        response = {
            "individual_validations": individual_validations,
            "collective_segmentation": collective_segmentation,
            "consolidated_score": {
                "total": total_score,
                "average": average_score
            }
        }
        return response
        
    except Exception as e:
        return {
            "status": "error",
            "message": f"Global error: {str(e)}"
        }
    finally:
        # Clean up temporary directory
        shutil.rmtree(temp_dir, ignore_errors=True)