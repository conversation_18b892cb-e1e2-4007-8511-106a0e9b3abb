from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, Request
from fastapi.security import OAuth2PasswordRe<PERSON>Form
from passlib.context import CryptContext
import boto3
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import os
from dotenv import load_dotenv
from datetime import datetime, timedelta, timezone
from jose import JWTError, jwt
from fastapi.security import OAuth2<PERSON><PERSON>wordBearer
from pydantic import BaseModel
from fastapi.middleware.cors import CORSMiddleware
from database_code.database import engine, Base, SessionLocal
from database_code.models import Users, UserSessions
from typing import Optional
# Create database tables
Base.metadata.create_all(bind=engine)

# Function to get database session
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Load environment variables
load_dotenv()

# AWS SES setup
AWS_REGION = os.getenv("AWS_REGION")
SMTP_SENDER_EMAIL = os.getenv("SMTP_SENDER_EMAIL")
AWS_ACCESS_KEY = os.getenv('AWS_ACCESS_KEY')
AWS_SECRET_KEY = os.getenv('AWS_SECRET_KEY')

SECRET_KEY = os.getenv("SECRET_KEY")  # Replace with a strong secret key
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_DAYS = 7 * 24 * 60 #10080
REFRESH_TOKEN_EXPIRE_DAYS = 15 * 24 * 60 #21600

SMTP_CLIENT = boto3.client(
    'ses',
    region_name=AWS_REGION,
    aws_access_key_id=AWS_ACCESS_KEY,
    aws_secret_access_key=AWS_SECRET_KEY,
)

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# FastAPI app
app = FastAPI()

class loginrequest(BaseModel):
    email: str
    password: str

class SignupRequest(BaseModel):
    title: str #should be "Mr.","Mrs.","Miss","Mx.","Master"
    first_name: str
    last_name: str
    phone_number: str
    age: int
    # gender: str #should be "male", "female", "other"
    postal_code: str
    county: str
    address: str
    email: str
    password: str
    role: Optional[str] = None

class Token(BaseModel):
    refresh_token: str

class ResendVerificationRequest(BaseModel):
    email: str

class SendResetPasswordRequest(BaseModel):
    email: str
class ResetPasswordRequest(BaseModel):
    token: str
    new_password: str
    confirm_password: str

# Password hashing helper
def hash_password(password: str):
    return pwd_context.hash(password)

def verify_password(plain_password: str, hashed_password: str):
    return pwd_context.verify(plain_password, hashed_password)

# Function to create tokens
def create_token(data: dict, expires_delta: int):
    to_encode = data.copy()
    expire = datetime.now(timezone.utc) + timedelta(minutes=expires_delta)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="login")


def get_current_user(token: str = Depends(oauth2_scheme), db: SessionLocal = Depends(get_db)):
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        email = payload.get("sub")
        if email is None:
            raise HTTPException(status_code=401, detail="Invalid access token.")
    except JWTError:
        raise HTTPException(status_code=401, detail="Invalid access token.")

    user = db.query(Users).filter(Users.email == email).first()
    if not user:
        raise HTTPException(status_code=401, detail="User not found.")

    user_session = db.query(UserSessions).filter(UserSessions.user_id == user.id).first()
    if not user_session or user_session.access_token != token:
        raise HTTPException(status_code=401, detail="Access token does not match the stored session or session expired.")
    
    # Update last_active
    user_session.last_active = datetime.now(timezone.utc)
    db.commit()

    return user

# Email sending helper
def send_verification_email(email: str):
    subject = "Verify Your Email Address"
    verification_token = create_token({"sub": email}, 2)  # Token expires in 15 minutes
    verification_link = f"http://**********:8000/auth/verify-email?token={verification_token}"
    body = f"""
    <html>
        <body>
            <h1>Email Verification</h1>
            <p>Thank you for signing up! Please verify your email address by clicking the link below:</p>
            <p><a href= "{verification_link}" target="_blank"> Verify Email</a></p>
        </body>
    </html>
    """
    
    msg = MIMEMultipart()
    msg["Subject"] = subject
    msg["From"] = SMTP_SENDER_EMAIL
    msg["To"] = email
    msg.attach(MIMEText(body, "html"))


    try:
        SMTP_CLIENT.send_email(
            Source=SMTP_SENDER_EMAIL,
            Destination={"ToAddresses": [email]},
            Message={
                "Subject": {"Data": subject},
                "Body": {"Html": {"Data": body}}
            },
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Email sending failed: {e}")

# Signup API
@app.post("/signup")
async def signup(request: SignupRequest, db: SessionLocal = Depends(get_db)):
    # Extract user details from request
    first_name = request.first_name
    last_name = request.last_name
    phone_number = request.phone_number
    age = request.age
    gender = request.gender
    postal_code = request.postal_code
    county = request.county
    address = request.address
    email = request.email
    passwords = request.password
    role = request.role

    # Check if the email already exists
    user = db.query(Users).filter(Users.email == email).first()
    if user:
        raise HTTPException(status_code=400, detail="Email already registered.")

    # Hash the password
    hashed_password = hash_password(passwords)

    try:
        # 1. Create the new user object
        new_user = Users(
            title=title,
            first_name=first_name,
            last_name=last_name,
            phone_number=phone_number,
            age=age,
            # gender=gender,
            postal_code=postal_code,
            county=county,
            address=address,
            email=email,
            passwords=hashed_password,
            role=role,
            is_verified="no",
            created_at=datetime.now(timezone.utc),
        )

        # 2. Add user to DB, but do NOT commit yet
        db.add(new_user)
        db.flush()  # Ensures new_user has an ID if needed

        # 3. Send verification email
        send_verification_email(email)  # Function should be defined elsewhere

        # 4. Commit only if the email was sent successfully
        db.commit()

        # 5. Create default session entry
        user_session = UserSessions(
            user_id=new_user.id,
            refresh_token="",  # Will be updated after login
            last_active=datetime.now(timezone.utc)
        )
        db.add(user_session)
        db.commit()

    except Exception as e:
        # If anything goes wrong, rollback
        db.rollback()
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create user or send email: {str(e)}"
        )

    return {"message": "User registered successfully. Please check your email for verification."}

# Login API
@app.post("/login")
async def login(request: loginrequest, db: SessionLocal = Depends(get_db)):
    email = request.email
    passwords = request.password
    # Check if the email exists in the database
    user = db.query(Users).filter(Users.email == email).first()
    if not user or not verify_password(passwords, user.passwords):
        raise HTTPException(status_code=401, detail="Invalid email or password.")
    
    # Check if the user is verified
    if user.is_verified != "yes":
        raise HTTPException(
            status_code=403,  # Forbidden
            detail="Your email is not verified. Please verify your email before logging in."
        )

    # Verify the password
    if not verify_password(passwords, user.passwords):
        raise HTTPException(status_code=401, detail="Invalid email or password.")
    
    # Generate access and refresh tokens
    access_token = create_token({"sub": user.email}, ACCESS_TOKEN_EXPIRE_DAYS)
    refresh_token = create_token({"sub": user.email}, REFRESH_TOKEN_EXPIRE_DAYS)

    # Update or create session
    user_session = db.query(UserSessions).filter(UserSessions.user_id == user.id).first()
    if user_session:
        user_session.refresh_token = refresh_token
        user_session.access_token = access_token
        user_session.last_active = datetime.now(timezone.utc)
    else:
        user_session = UserSessions(
            user_id=user.id,
            refresh_token=refresh_token,
            access_token=access_token,
            last_active=datetime.now(timezone.utc)
        )
        db.add(user_session)
    db.commit()
    # Return the tokens in the response
    return {
        "access_token": access_token,
        "refresh_token": refresh_token,
        "role":user.role,
        "token_type": "bearer",
        "user_email":user.email,
        "user_first_name":user.first_name,
        "message":"Login Successful"
    }

@app.post("/refresh-token")
async def refresh_token(request: Token, db: SessionLocal = Depends(get_db)):
    refresh_token = request.refresh_token
    try:
        payload = jwt.decode(refresh_token, SECRET_KEY, algorithms=[ALGORITHM])
        email = payload.get("sub")
        if email is None:
            print(payload, "email is none")
            raise HTTPException(status_code=401, detail="Invalid refresh token 1.") 
    except JWTError:
        print("jwt error")
        raise HTTPException(status_code=401, detail="Invalid refresh token 2.")

    # Verify refresh token in DB
    user = db.query(Users).filter(Users.email == email).first()
    user_session = db.query(UserSessions).filter(UserSessions.user_id == user.id).first()
    if not user_session or user_session.refresh_token != refresh_token:
        raise HTTPException(status_code=401, detail="Invalid refresh token3.")
    
    # Ensure last_active is timezone-aware and validate activity
    if user_session.last_active.tzinfo is None:
        user_session.last_active = user_session.last_active.replace(tzinfo=timezone.utc)

    # Invalidate session if inactive for 15 minutes
    if (datetime.now(timezone.utc) - user_session.last_active) > timedelta(minutes=REFRESH_TOKEN_EXPIRE_DAYS):
        db.delete(user_session)
        db.commit()
        raise HTTPException(status_code=401, detail="Session expired. Please log in again.")

    # Update last active and generate new tokens
    user_session.last_active = datetime.now(timezone.utc)
    


    access_token = create_token({"sub": email}, ACCESS_TOKEN_EXPIRE_DAYS)
    user_session.access_token = access_token
    db.commit()
    # here refresh token was getting updated as the new access token is generated which was getting added to the db which then causing the logout 
   
    # new_refresh_token = create_token({"sub": email}, REFRESH_TOKEN_EXPIRE_DAYS)
    # user_session.refresh_token = new_refresh_token
    # db.commit()
    return {"access_token":access_token,"refresh_token": refresh_token, "token_type": "bearer"}


@app.post("/validate-token")
async def validate_token(current_user: Users = Depends(get_current_user)):
    """
    If get_current_user does not raise an exception,
    the token is valid and matches the token in the database.
    """
    return {
        "message": "Token is valid.",
        "user_email": current_user.email}

@app.post("/refresh-session")
async def refresh_session(token: str = Depends(oauth2_scheme), db: SessionLocal = Depends(get_db)):
    try:
        # Decode the token to get the user email
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        email = payload.get("sub")
        if not email:
            raise HTTPException(status_code=401, detail="Invalid token.")

        # Fetch user session
        user = db.query(Users).filter(Users.email == email).first()
        if not user:
            raise HTTPException(status_code=401, detail="User not found.")
        
        user_session = db.query(UserSessions).filter(UserSessions.user_id == user.id).first()
        if not user_session:
            raise HTTPException(status_code=401, detail="Session expired. Please log in again.")

        # Update last_active time
        user_session.last_active = datetime.now(timezone.utc)
        db.commit()
        
        return {"message": "Session refreshed successfully."}
    except JWTError:
        raise HTTPException(status_code=401, detail="Invalid token.")

@app.get("/protected-route")
async def protected_route(current_user: Users = Depends(get_current_user)):
    return {"message": f"Hello, {current_user.first_name}!"}

@app.get("/test-token")
async def test_token(token: str = Depends(oauth2_scheme)):
    try:
        # Decode the token
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        print(payload)  # Debug: Print the payload
        email = payload.get("sub")
        print(email)  # Debug: Print the email
        return {"email": email, "payload": payload}
    except JWTError as e:
        raise HTTPException(status_code=401, detail=f"Invalid token: {str(e)}")
    
@app.get("/verify-email")
async def verify_email(token: str, db: SessionLocal = Depends(get_db)):
    try:
        # Decode the token
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        email = payload.get("sub")
        if not email:
            raise HTTPException(status_code=401, detail="Invalid token.")

        # Fetch the user and update verification status
        user = db.query(Users).filter(Users.email == email).first()
        if not user:
            raise HTTPException(status_code=404, detail="User not found.")
        
        if user.is_verified == "yes":
            return {"message": "User is already verified."}

        user.is_verified = "yes"
        db.commit()
        return {"message": "Email verified successfully."}
    except JWTError:
        raise HTTPException(status_code=401, detail="Verification link has expired or is invalid.")

@app.post("/resend-verification")
async def resend_verification(request: ResendVerificationRequest, db: SessionLocal = Depends(get_db)):
    email = request.email
    # Check if the user exists and is not verified
    user = db.query(Users).filter(Users.email == email).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found.")
    
    if user.is_verified == "yes":
        raise HTTPException(status_code=400, detail="Email is already verified.")

    # Resend the verification email
    send_verification_email(email)
    return {"message": "Verification email sent successfully. Please check your inbox."}

@app.post("/request-password-reset")
async def request_password_reset(request: SendResetPasswordRequest, db: SessionLocal = Depends(get_db)):
    email = request.email
    # Check if the user exists
    user = db.query(Users).filter(Users.email == email).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found.")

    # Generate a reset token valid for 15 minutes
    reset_token = create_token({"sub": email}, expires_delta=15)

    # Reset password link
    reset_link = f"http://**********:80/reset?token={reset_token}"

    # Email body
    subject = "Reset Your Password"
    body = f"""
    <html>
        <body>
            <h1>Reset Your Password</h1>
            <p>Click the link below to reset your password. This link will expire in 15 minutes:</p>
            <a href="{reset_link}" target="_blank">Reset Password</a>
        </body>
    </html>
    """
    try:
        SMTP_CLIENT.send_email(
            Source=SMTP_SENDER_EMAIL,
            Destination={"ToAddresses": [email]},
            Message={
                "Subject": {"Data": subject},
                "Body": {"Html": {"Data": body}},
            },
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Email sending failed: {e}")

    return {"message": "Password reset email sent. Please check your inbox.",
            "token":reset_token}

@app.post("/reset-password")
async def reset_password(request: ResetPasswordRequest, db: SessionLocal = Depends(get_db)):
    # Decode the token
    try:
        payload = jwt.decode(request.token, SECRET_KEY, algorithms=[ALGORITHM])
        email = payload.get("sub")
        if not email:
            raise HTTPException(status_code=401, detail="Invalid token.")
    except JWTError:
        raise HTTPException(status_code=401, detail="Invalid or expired token.")

    # Check if the user exists
    user = db.query(Users).filter(Users.email == email).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found.")

    # Verify new_password and confirm_password match
    if request.new_password != request.confirm_password:
        raise HTTPException(status_code=400, detail="Passwords do not match.")

    # Hash and update the new password
    hashed_password = hash_password(request.new_password)
    user.passwords = hashed_password
    db.commit()

    return {"message": "Password reset successfully."}

@app.post("/logout")
async def logout(current_user: Users = Depends(get_current_user), db: SessionLocal = Depends(get_db)):
    user_session = db.query(UserSessions).filter(UserSessions.user_id == current_user.id).first()
    if not user_session:
        raise HTTPException(status_code=401, detail="User not logged in.")
    
    db.delete(user_session)
    db.commit()
    
    return {"message": "User logged out successfully."}
