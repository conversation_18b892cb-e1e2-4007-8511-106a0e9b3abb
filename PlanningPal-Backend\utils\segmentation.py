import cv2
import numpy as np
import os
import re
import fitz  # PyMuPDF
from paddleocr import PaddleOCR
import io
from PIL import Image
import boto3
import tempfile
from botocore.exceptions import ClientError
from core.config import s3_client, S3_BUCKET_NAME
import uuid
from datetime import datetime
import shutil

# ----------------- CONFIG -----------------
ocr = PaddleOCR(
    use_gpu=True,  # Enable GPU
    gpu_mem=1024,  # Allocate GPU memory
    use_angle_cls=True, lang='en'
)
CHECK = {
    "existing floor plan": ["existing", "floor", "plan"],
    "proposed floor plan": ["proposed", "floor", "plan"],
    "existing elevation": ["existing", "elevation"],
    "proposed elevation": ["proposed", "elevation"],
    "existing roof plan": ["existing", "roof", "plan"],
    "proposed roof plan": ["proposed", "roof", "plan"]
}


# ----------------- OCR -----------------
def run_ocr(image):
    result = ocr.ocr(image, cls=True)
    lines = []
    if result and result[0]:
        for box, (text, score) in result[0]:
            if score > 0.3:
                lines.append({'text': text, 'box': box})
    return lines

# ----------------- Label Normalization -----------------
def normalize_label(text):
    """Determine the plan type using regex pattern matching."""
    if text is None:
        return None
        
    text = str(text).lower()
    
    # Define plan type patterns
    patterns = {
        "existing floor plan": ["existing.*floor.*plan", "existing.*plan.*floor", "floor.*existing.*plan"],
        "proposed floor plan": ["proposed.*floor.*plan", "proposed.*plan.*floor", "floor.*proposed.*plan"],
        "existing elevation": ["existing.*elevation", "elevation.*existing"],
        "proposed elevation": ["proposed.*elevation", "elevation.*proposed"],
        "existing roof plan": ["existing.*roof.*plan", "existing.*plan.*roof", "roof.*existing.*plan"],
        "proposed roof plan": ["proposed.*roof.*plan", "proposed.*plan.*roof", "roof.*proposed.*plan"]
    }
    
    # Check each pattern
    for plan_type, pattern_list in patterns.items():
        for pattern in pattern_list:
            if re.search(pattern, text, re.IGNORECASE):
                return plan_type
    
    # Fallback checks for partial matches
    if "existing" in text:
        if "floor" in text or "ground" in text:
            return "existing floor plan"
        if "elevation" in text:
            return "existing elevation"
        if "roof" in text:
            return "existing roof plan"
    elif "proposed" in text:
        if "floor" in text or "ground" in text:
            return "proposed floor plan"
        if "elevation" in text:
            return "proposed elevation"
        if "roof" in text:
            return "proposed roof plan"
        if "plan" in text:
            return "proposed floor plan"
        return "proposed elevation"
    
    return "unknown"

# ----------------- Group Nearby Text -----------------
def group_text_near_segment(text_lines, x, y, w, h):
    matched = []
    # Increase the search area for text
    search_margin_x = 100  # Increased from 80
    search_margin_y = 80   # Increased from 60
    
    for item in text_lines:
        coords = np.array(item['box'])[:, :2]
        cx, cy = np.mean(coords, axis=0)
        if x - search_margin_x <= cx <= x + w + search_margin_x and y - search_margin_y <= cy <= y + h + search_margin_y:
            matched.append(item['text'])
    return ' '.join(matched)

# ----------------- Segmentation -----------------
def segment_and_label(image, label_set):
    scale_width = 2400  # Increased from 1800 for better resolution
    scale_factor = scale_width / image.shape[1]
    scaled_image = cv2.resize(image, (scale_width, int(image.shape[0] * scale_factor)))

    gray = cv2.cvtColor(scaled_image, cv2.COLOR_BGR2GRAY)
    # Enhance contrast
    clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
    gray = clahe.apply(gray)
    
    thresh = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                   cv2.THRESH_BINARY_INV, 11, 2)
    cleaned = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, np.ones((5, 5), np.uint8))

    contours, _ = cv2.findContours(cleaned, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    filtered = [c for c in contours if cv2.contourArea(c) > 5000]
    sorted_ctrs = sorted(filtered, key=lambda c: cv2.boundingRect(c)[1])

    ocr_lines = run_ocr(scaled_image)
    annotated_image = scaled_image.copy()
    segments = []

    for i, ctr in enumerate(sorted_ctrs):
        x, y, w, h = cv2.boundingRect(ctr)
        label_text = group_text_near_segment(ocr_lines, x, y, w, h)
        label = normalize_label(label_text)
        
        # Only add valid labels to the set
        if label and label != "unknown":
            label_set.add(str(label))

        # Get original size coordinates
        x_orig = int(x / scale_factor)
        y_orig = int(y / scale_factor)
        w_orig = int(w / scale_factor)
        h_orig = int(h / scale_factor)
        
        # Draw on annotated image
        cv2.rectangle(annotated_image, (x, y), (x + w, y + h), (0, 255, 0), 2)
        cv2.putText(annotated_image, str(label or "UNKNOWN"), (x, y - 10),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)

        segment_crop = image[y_orig:y_orig+h_orig, x_orig:x_orig+w_orig]
        
        # Convert segment to bytes
        _, img_encoded = cv2.imencode('.jpg', segment_crop)
        img_bytes = img_encoded.tobytes()
        
        # Upload segment to S3
        segment_key = f"segments/{uuid.uuid4()}_{str(label or f'segment_{i+1}')}.jpg"
        s3_client.put_object(
            Bucket=S3_BUCKET_NAME,
            Key=segment_key,
            Body=img_bytes,
            ContentType='image/jpeg'
        )
        
        segments.append({
            'label': str(label) if label else None,
            'segment_key': str(segment_key),
            'text': str(label_text),
            'x': x_orig,
            'y': y_orig,
            'width': w_orig,
            'height': h_orig
        })

    # Upload annotated image
    _, annotated_encoded = cv2.imencode('.jpg', annotated_image)
    annotated_key = f"annotated/{uuid.uuid4()}_annotated.jpg"
    s3_client.put_object(
        Bucket=S3_BUCKET_NAME,
        Key=str(annotated_key),
        Body=annotated_encoded.tobytes(),
        ContentType='image/jpeg'
    )

    return segments, str(annotated_key)

# ----------------- Extract Images from PDF -----------------
def extract_images_from_pdfs(pdf_keys):
    """
    Extract and process images from multiple PDFs
    
    Args:
        pdf_keys (List[str]): List of S3 keys for the PDF files
        
    Returns:
        tuple: (segments, annotated_images, labels)
    """
    all_segments = []
    all_annotated_images = []
    combined_labels = set()
    
    try:
        for pdf_key in pdf_keys:
            temp_dir = tempfile.mkdtemp()
            temp_pdf_path = os.path.join(temp_dir, f"{uuid.uuid4()}.pdf")
            
            try:
                # Download PDF from S3
                s3_client.download_file(S3_BUCKET_NAME, str(pdf_key), temp_pdf_path)
                
                # Process the PDF
                doc = None
                try:
                    doc = fitz.open(temp_pdf_path)
                    for page_num in range(len(doc)):
                        page = doc[page_num]
                        pix = page.get_pixmap(matrix=fitz.Matrix(300/72, 300/72))
                        
                        # Convert PyMuPDF pixmap to bytes
                        img_data = pix.samples
                        
                        # Create numpy array from bytes
                        np_arr = np.frombuffer(img_data, dtype=np.uint8)
                        
                        # Reshape array to image dimensions
                        if pix.n == 1:
                            # Grayscale
                            img = np_arr.reshape(pix.height, pix.width)
                            img = cv2.cvtColor(img, cv2.COLOR_GRAY2BGR)
                        elif pix.n == 3:
                            # RGB
                            img = np_arr.reshape(pix.height, pix.width, 3)
                        elif pix.n == 4:
                            # RGBA
                            img = np_arr.reshape(pix.height, pix.width, 4)
                            img = cv2.cvtColor(img, cv2.COLOR_RGBA2BGR)
                        else:
                            print(f"Unsupported number of channels: {pix.n}")
                            continue
                        
                        if img is None or img.size == 0:
                            print(f"Failed to convert page {page_num} of PDF {pdf_key}")
                            continue
                        
                        # Process the image
                        try:
                            segments, annotated_key = segment_and_label(img, combined_labels)
                            
                            # Add source information to segments
                            for segment in segments:
                                segment['source_pdf'] = str(pdf_key)
                                segment['page_number'] = str(page_num)
                                # Ensure all values are strings
                                segment['label'] = str(segment['label']) if segment.get('label') is not None else None
                                segment['segment_key'] = str(segment['segment_key'])
                                segment['text'] = str(segment['text'])
                            
                            all_segments.extend(segments)
                            all_annotated_images.append({
                                'source_pdf': str(pdf_key),
                                'page_number': str(page_num),
                                'annotated_key': str(annotated_key)
                            })
                        except Exception as e:
                            print(f"Error processing page {page_num}: {str(e)}")
                            continue
                            
                finally:
                    if doc:
                        doc.close()
                        
            except Exception as e:
                print(f"Error processing PDF {pdf_key}: {str(e)}")
                continue
                
            finally:
                # Cleanup: Close file handles and remove temporary files
                try:
                    if os.path.exists(temp_pdf_path):
                        os.unlink(temp_pdf_path)
                    if os.path.exists(temp_dir):
                        os.rmdir(temp_dir)
                except Exception as e:
                    print(f"Error cleaning up temporary files: {str(e)}")
        
        # Convert combined_labels to list of strings
        combined_labels_list = [str(label) for label in combined_labels]
        
        return all_segments, all_annotated_images, combined_labels_list
        
    except Exception as e:
        print(f"Error in combined segmentation: {str(e)}")
        return [], [], []

# ----------------- Main Multi-PDF Processor -----------------
def process_multiple_pdfs(pdf_keys):
    """Process multiple PDFs and generate a combined validation report.
    
    Args:
        pdf_keys (List[str]): List of S3 keys for the PDF files
    """
    try:
        # Process PDFs and get labels
        segments, _, labels = extract_images_from_pdfs(pdf_keys)
        
        # Reset checklist
        for key in CHECK.keys():
            CHECK[key] = False
        
        # Update checklist based on all found labels
        for label in labels:
            label = str(label).lower()
            for checklist_item in CHECK.keys():
                if checklist_item in label:
                    CHECK[checklist_item] = True
        
        # Generate checklist validation
        checklist_validation = {}
        for key, found in CHECK.items():
            checklist_validation[key] = "✅ FOUND" if found else "❌ MISSING"
        
        # Return the validation report
        return {
            "checklist_validation": checklist_validation
        }
        
    except Exception as e:
        print(f"Error in process_multiple_pdfs: {str(e)}")
        return {
            "checklist_validation": {key: "❌ ERROR" for key in CHECK.keys()}
        }
    # No cleanup needed here; handled in extract_images_from_pdfs