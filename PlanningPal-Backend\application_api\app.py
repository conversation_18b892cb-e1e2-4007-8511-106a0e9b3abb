from datetime import UTC, datetime
from fastapi import FastAPI, UploadFile, File, HTTPException, Header, Depends, Form
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import Optional, List
from email.mime.text import MIMEText
from email.mime.multipart import <PERSON><PERSON><PERSON>ultipart
from database_code.database import engine, Base, SessionLocal
from database_code.models import Users, UserSessions, UserApplicationDetails, UserApplicationStatus, CouncilApplicationMap, ApplicationTypes, UserInputs, FieldMaster, TypeMaster, PreFlightMaster, SectionMaster, CouncilDetails, CouncilComments, Notification
from typing import Annotated
from dotenv import load_dotenv
import os
from botocore.exceptions import ClientError
import boto3 # type: ignore
from math import ceil
import uuid
import json
from core.config import s3_client, S3_BUCKET_NAME
from models.request_models import DocumentValidationRequest
from models.response_models import ValidationResult, IndividualValidation
from core.validator import validate, validate_multiple
from utils import orientation, ocr_extraction, segmentation
import requests
import tempfile
import shutil
from urllib.parse import urlparse
from io import BytesIO
from reportlab.lib.pagesizes import A4
from reportlab.lib.utils import ImageReader
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, KeepTogether
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.lib import colors
from reportlab.pdfgen import canvas
from PyPDF2 import PdfMerger
from sqlalchemy import func, case
from fastapi import Query

# Database configuration
Base.metadata.create_all(bind=engine)

app = FastAPI(debug=True)

load_dotenv()
AWS_ACCESS_KEY = os.getenv('AWS_ACCESS_KEY')
AWS_SECRET_KEY = os.getenv('AWS_SECRET_KEY')
AWS_REGION_S3 = os.getenv('AWS_REGION_S3')
S3_BUCKET_NAME = os.getenv('S3_BUCKET_NAME')
SMTP_SENDER_EMAIL = os.getenv('SMTP_SENDER_EMAIL')
SMTP_CLIENT = boto3.client('ses', region_name=os.getenv('AWS_REGION'),aws_access_key_id=AWS_ACCESS_KEY,aws_secret_access_key=AWS_SECRET_KEY)

s3_client = boto3.client(
    "s3",
    aws_access_key_id=AWS_ACCESS_KEY,
    aws_secret_access_key=AWS_SECRET_KEY,
    region_name=AWS_REGION_S3
)

#---------Notification Trigger----------
def trigger_notification(
    db: Session,
    user_id: int,
    notification_text: str,
    status_id: int,
    user_application_id: int,
):
    new_notification = Notification(
        user_id=user_id,
        notification_text=notification_text,
        status_id=status_id,
        user_application_id=user_application_id,
        created_at=datetime.utcnow(),
        read_status=False
    )
    db.add(new_notification)
    db.commit()
    db.refresh(new_notification)
    return new_notification

def get_admin_user_ids(db: Session):
    """Return a list of all admin user IDs."""
    return [u.id for u in db.query(Users).filter(Users.role == "admin").all()]


# Pydantic model for input request
class UserApplicationRequest(BaseModel):
    application_type_id: int  # This is council_application_map.id
    application_name: str
    site_post_code: str
    site_address: str
    site_town_city: str
    site_county: str
    site_easting: str
    site_northing: str

class UserData(BaseModel):
    field_id: int
    field_name: str
    field_value: str

class HabitatMetricsInput(BaseModel):
    application_id: int
    row_id: int
    habitat_type: str
    broad_habitat_group: str
    strategic_significance: str
    total_area: float
    area_retained: float
    area_enhanced: float

# Pydantic model for request body
class UserApplicationID(BaseModel):
    user_application_id: int
    # field_ids: list[int]

class CommentsRequest(BaseModel):
    user_application_id: int
    comment: str

class FetchStepFieldsRequest(BaseModel):
    user_application_id: int
    section_ids: List[int]

class MappedResponseRequest(BaseModel):
    user_application_id: int
    section_ids: List[int]

class CommentRequest(BaseModel):
    user_application_id: int
    flag: Optional[bool] = None
    comments: Optional[str] = None
class PDFGenerationResponse(BaseModel):
    file_key: str

class AssignPlanner(BaseModel):
    user_application_id: int
    planner_id: int

# Function to get database session
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def get_field_mapping(db: Session):
    fields = db.query(FieldMaster).all()
    return {field.id: field.name for field in fields}


def get_application_mapping(db: Session):
    apps = db.query(UserApplicationDetails).all()
    return {app.id: app.application_name for app in apps}


def get_section_mapping(db: Session):
    sections = db.query(SectionMaster).all()
    return {str(section.id): section.name for section in sections}


# Bedrock client
AWS_REGION = os.getenv("AWS_REGION")
AWS_ACCESS_KEY = os.getenv('AWS_ACCESS_KEY')
AWS_SECRET_KEY = os.getenv('AWS_SECRET_KEY')
MODEL_ID = os.getenv("MODEL_ID")

# ---- Initialize Bedrock client & embeddings ----
bedrock_client = boto3.client(service_name="bedrock-runtime",region_name=AWS_REGION,aws_access_key_id=os.getenv("AWS_ACCESS_KEY"),
    aws_secret_access_key=os.getenv("AWS_SECRET_KEY"))

def send_application_status_email(email: str, user_role: str, is_approved: bool, comments: Optional[str] = None, certificate_url: Optional[str] = None):
    """
    Send an email to the user about their application status.
    
    Args:
        email (str): Recipient's email address
        user_role (str): Role of the person who validated/approved (e.g., 'admin', 'planner')
        is_approved (bool): Whether the application was approved or rejected
        comments (Optional[str]): Comments/suggestions if the application was rejected
        certificate_url (Optional[str]): Presigned URL to access the certificate
    """
    subject = "Application Status Update"
    
    # Determine the status message based on role and approval
    if user_role == "admin":
        if is_approved:
            status_message = "Your application has been validated by the admin."
            body = f"""
            <html>
                <body>
                    <h1>Application Validated</h1>
                    <p>{status_message}</p>
                    <p>Your application has passed the initial validation and will now be reviewed by a planner.</p>
                    {f'<p><strong>Certificate:</strong> <a href="{certificate_url}" target="_blank">Click here to view your validation certificate</a></p>' if certificate_url else ''}
                </body>
            </html>
            """
        else:
            status_message = "Your application has been invalidated by the admin and requires attention."
            body = f"""
            <html>
                <body>
                    <h1>Application Requires Attention</h1>
                    <p>{status_message}</p>
                    <p>Please review the following comments and make necessary changes:</p>
                    <div style="background-color: #f5f5f5; padding: 15px; border-left: 4px solid #ff0000;">
                        <p>{comments if comments else "No specific comments provided."}</p>
                    </div>
                    <p>Please make the required changes and resubmit your application.</p>
                    {f'<p><strong>Certificate:</strong> <a href="{certificate_url}" target="_blank">Click here to view your invalidation certificate</a></p>' if certificate_url else ''}
                </body>
            </html>
            """
    else:  # planner role
        if is_approved:
            status_message = "Your application has been approved by the planner."
            body = f"""
            <html>
                <body>
                    <h1>Application Approved</h1>
                    <p>{status_message}</p>
                    <p>Your application has been fully approved. You can now proceed with the next steps in your planning process.</p>
                    {f'<p><strong>Certificate:</strong> <a href="{certificate_url}" target="_blank">Click here to view your approval certificate</a></p>' if certificate_url else ''}
                </body>
            </html>
            """
        else:
            status_message = "Your application has been refused by the planner and requires attention."
            body = f"""
            <html>
                <body>
                    <h1>Application Refused</h1>
                    <p>{status_message}</p>
                    <p>Please review the following comments and make necessary changes:</p>
                    <div style="background-color: #f5f5f5; padding: 15px; border-left: 4px solid #ff0000;">
                        <p>{comments if comments else "No specific comments provided."}</p>
                    </div>
                    <p>Please make the required changes and resubmit your application.</p>
                    {f'<p><strong>Certificate:</strong> <a href="{certificate_url}" target="_blank">Click here to view your refusal certificate</a></p>' if certificate_url else ''}
                </body>
            </html>
            """
    
    try:
        SMTP_CLIENT.send_email(
            Source=SMTP_SENDER_EMAIL,
            Destination={"ToAddresses": [email]},
            Message={
                "Subject": {"Data": subject},
                "Body": {"Html": {"Data": body}}
            },
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Email sending failed: {e}")

def generate_certificate_for_application(
    user_application_id: int,
    user_role: str,
    is_approved: bool,
    comments: Optional[str] = None,
    db: Session = None
):
    close_db = False
    if db is None:
        db = SessionLocal()
        close_db = True

    try:
        # --- Fetch application info from DB ---
        app = db.query(UserApplicationDetails).filter(UserApplicationDetails.id == user_application_id).first()
        if not app:
            raise ValueError("Application not found")
        # Fetch applicant
        applicant = db.query(Users).filter(Users.id == app.user_id).first()
        # Fetch assigned user (planner)
        planner = db.query(Users).filter(Users.id == app.assigned_to).first() if app.assigned_to else None
        # Fetch application type
        council_app_map = db.query(CouncilApplicationMap).filter(CouncilApplicationMap.id == app.application_id).first()
        app_type = db.query(ApplicationTypes).filter(ApplicationTypes.id == council_app_map.application_id).first() if council_app_map else None

        # Officer/planner info for certificate
        if user_role == "admin":
            # Use fixed admin details
            officer_name = "John Doe"  # Replace with actual admin name if needed
            contact_email = "<EMAIL>"
        else:  # user_role == "planner"
            # Use assigned planner details
            officer_name = f"{planner.first_name} {planner.last_name}" if planner else ""
            contact_email = planner.email if planner else ""

        # Fetch description of proposed work from UserInputs
        desc_input = db.query(UserInputs).filter(
            UserInputs.user_application_id == user_application_id,
            UserInputs.field == 22
        ).first()
        description_of_work = desc_input.user_input if desc_input else ""

        # Fetch applicant info from UserInputs
        def get_input_value(field_id):
            row = db.query(UserInputs).filter(
                UserInputs.user_application_id == user_application_id,
                UserInputs.field == field_id
            ).first()
            return row.user_input if row else ""

        applicant_first_name = get_input_value(10)
        applicant_last_name = get_input_value(11)
        applicant_name = f"{applicant_first_name} {applicant_last_name}".strip()

        address_line_1 = get_input_value(12)
        address_line_2 = get_input_value(13)
        postcode = get_input_value(14)
        town_city = get_input_value(15)
        county = get_input_value(16)
        applicant_address_lines = [address_line_1, address_line_2, postcode, town_city, county]
        applicant_address_lines = [line for line in applicant_address_lines if line]  # Remove empty

        application = {
            "site_name": app.application_name,
            "site_address_lines": [app.site_address, app.site_post_code],
            "description_of_work": description_of_work,
            "applicant_name": applicant_name,
            "applicant_address_lines": applicant_address_lines,
            "officer_name": officer_name,
            "contact_email": contact_email,
            "application_type": app_type.kind if app_type else "",
        }

        # --- Determine certificate type and wording ---
        if user_role == "admin":
            if is_approved:
                certificate_title = "VALIDATION CERTIFICATE"
                body_text = (
                    "This certificate acknowledges the following property has been validated as per council requirements:"
                )
                validation_text = [
                    "The application has been validated against national and local planning requirements.",
                    "It is confirmed as VALID and will now proceed to the assessment stage."
                ]
                show_comments = False
            else:
                certificate_title = "INVALIDATION CERTIFICATE"
                body_text = (
                    "This certificate notifies that the following property has NOT been validated as per council requirements:"
                )
                validation_text = [
                    "The application has been reviewed and found INVALID against national and local planning requirements.",
                    "Please see the comments below and resubmit your application after making the required changes."
                ]
                show_comments = True
        else:
            if is_approved:
                certificate_title = "APPROVAL CERTIFICATE"
                body_text = (
                    "This certificate confirms that the following application has been APPROVED by the planning authority:"
                )
                validation_text = [
                    "The application has been approved and you may proceed with the next steps.",
                    "Please retain this certificate for your records."
                ]
                show_comments = False
            else:
                certificate_title = "REFUSAL CERTIFICATE"
                body_text = (
                    "This certificate notifies that the following application has been REFUSED by the planning authority:"
                )
                validation_text = [
                    "The application has been reviewed and REFUSED.",
                    "Please see the comments below and make the required changes before resubmission."
                ]
                show_comments = True

        # --- Generate the certificate using the existing function ---
        pdf_buffer = BytesIO()
        c = canvas.Canvas(pdf_buffer, pagesize=A4)
        width, height = A4

        # --- Static values ---
        # Get the directory where your app.py is located
        current_dir = os.path.dirname(os.path.abspath(__file__))
        logo_path = os.path.join(current_dir, "logo", "logo.png")
        council_name = "NEWARK AND SHERWOOD DISTRICT COUNCIL"
        application_type = "Householder Planning Application"
        department_name = "NEWARK AND SHERWOOD DISTRICT COUNCIL"

        # --- Position constants ---
        left_margin = 70
        field_indent = 180
        line_gap = 18
        paragraph_gap = line_gap * 1.5

        # --- Logo and Header (side by side) ---
        logo_width = 90
        logo_height = 90
        logo_y = height - 130
        if os.path.exists(logo_path):
            logo = ImageReader(logo_path)
            c.drawImage(logo, left_margin, logo_y, width=logo_width, height=logo_height, preserveAspectRatio=True, mask='auto')

        text_y = logo_y + 40
        c.setFont("Helvetica-Bold", 14)
        c.drawString(left_margin + logo_width + 20, text_y, council_name)

        c.setFont("Helvetica-Bold", 16)
        c.drawString(left_margin + logo_width + 20, text_y - 25, certificate_title)

        # --- Horizontal line and spacing ---
        line_y = text_y - 35
        c.line(left_margin, line_y, width - left_margin, line_y)

        y = line_y - 30

        # --- Application Type ---
        c.setFont("Helvetica-Bold", 11)
        c.drawString(left_margin, y, "Application Type:")
        c.setFont("Helvetica", 11)
        c.drawString(field_indent, y, application_type)
        y -= paragraph_gap

        # --- Certificate Body Text ---
        c.setFont("Helvetica", 11)
        c.drawString(left_margin, y, body_text)
        y -= paragraph_gap

        # --- Site Name ---
        c.setFont("Helvetica-Bold", 11)
        c.drawString(left_margin, y, "Site Name:")
        c.setFont("Helvetica", 11)
        c.drawString(field_indent, y, application["site_name"])
        y -= line_gap

        # --- Site Address ---
        full_site_address = ", ".join(application["site_address_lines"])
        c.setFont("Helvetica-Bold", 11)
        c.drawString(left_margin, y, "Site Address:")
        c.setFont("Helvetica", 11)
        c.drawString(field_indent, y, full_site_address)
        y -= paragraph_gap

        # --- Description of Proposed Work ---
        c.setFont("Helvetica-Bold", 11)
        c.drawString(left_margin, y, "Description of Proposed Work:")
        y -= line_gap
        c.setFont("Helvetica", 11)
        max_width = width - field_indent - left_margin
        words = application["description_of_work"].split()
        lines = []
        current_line = ""
        for word in words:
            test_line = current_line + " " + word if current_line else word
            if c.stringWidth(test_line, "Helvetica", 11) <= max_width:
                current_line = test_line
            else:
                lines.append(current_line)
                current_line = word
        if current_line:
            lines.append(current_line)
        for line in lines:
            c.drawString(field_indent, y, line)
            y -= line_gap

        y -= line_gap

        # --- Applicant Info ---
        c.setFont("Helvetica", 11)
        if user_role == "admin" and not is_approved:
            c.drawString(left_margin, y, "The application has been reviewed and rejected for the following individual:")
        else:
            c.drawString(left_margin, y, "The application has been reviewed for the following individual:")
        y -= paragraph_gap

        c.setFont("Helvetica-Bold", 11)
        c.drawString(left_margin, y, "Applicant Name:")
        c.setFont("Helvetica", 11)
        c.drawString(field_indent, y, application["applicant_name"])
        y -= line_gap

        full_applicant_address = ", ".join(application["applicant_address_lines"])
        c.setFont("Helvetica-Bold", 11)
        c.drawString(left_margin, y, "Applicant Address:")
        c.setFont("Helvetica", 11)
        c.drawString(field_indent, y, full_applicant_address)
        y -= paragraph_gap

        # --- Validation/Approval/Refusal Text ---
        c.setFont("Helvetica", 11)
        for vt in validation_text:
            c.drawString(left_margin, y, vt)
            y -= line_gap

        # --- Comments for Invalidation/Refusal ---
        if show_comments:
            y -= line_gap
            c.setFont("Helvetica-Bold", 11)
            c.drawString(left_margin, y, "Comments:")
            y -= line_gap
            c.setFont("Helvetica", 11)
            
            # Get comments text
            comments_text = comments or "No specific comments provided."
            
            # Calculate available width for comments (with indentation)
            comment_indent = left_margin + 5
            max_comment_width = width - comment_indent - left_margin
            
            # Split comments into individual comments first (if it's a joined string)
            if isinstance(comments_text, str) and "\n" in comments_text:
                individual_comments = comments_text.split("\n")
            else:
                individual_comments = [comments_text]
            
            # Process each comment separately
            for comment in individual_comments:
                if not comment.strip():  # Skip empty comments
                    continue
                    
                # Split this comment into words and create wrapped lines
                words = comment.split()
                comment_lines = []
                current_line = ""
                
                for word in words:
                    test_line = current_line + " " + word if current_line else word
                    if c.stringWidth(test_line, "Helvetica", 11) <= max_comment_width:
                        current_line = test_line
                    else:
                        if current_line:
                            comment_lines.append(current_line)
                        current_line = word
                
                # Add the last line if it exists
                if current_line:
                    comment_lines.append(current_line)
                
                # Draw each comment line with proper indentation
                for line in comment_lines:
                    c.drawString(comment_indent, y, line)
                    y -= line_gap
                
                # Add small spacing between comments (reduced from full line_gap)
                y -= line_gap * 0.1

        y -= paragraph_gap

        # --- Officer/Department Details ---
        c.setFont("Helvetica-Bold", 11)
        c.drawString(left_margin, y, "Issuing Officer:")
        c.setFont("Helvetica", 11)
        c.drawString(field_indent, y, application["officer_name"])
        y -= line_gap

        c.setFont("Helvetica-Bold", 11)
        c.drawString(left_margin, y, "Department:")
        c.setFont("Helvetica", 11)
        c.drawString(field_indent, y, department_name)
        y -= line_gap

        c.setFont("Helvetica-Bold", 11)
        c.drawString(left_margin, y, "Email:")
        c.setFont("Helvetica", 11)
        c.drawString(field_indent, y, application["contact_email"])
        y -= paragraph_gap

        # --- Disclaimer ---
        if user_role == "admin" and is_approved:
            c.setFont("Helvetica-Oblique", 10)
            c.drawString(left_margin, y, "Note: Validation does not imply planning approval.")
            y -= line_gap
            c.drawString(left_margin, y, "Your application will now proceed to formal determination.")

        # --- Footer: Date and Signature ---
        today = datetime.today().strftime('%d %B %Y')
        c.setFont("Helvetica", 10)
        c.drawString(left_margin, 80, f"Issued on: {today}")
        c.drawRightString(width - left_margin, 80, "Signature: ____________________")

        c.save()
        pdf_content = pdf_buffer.getvalue()
        pdf_buffer.close()

        # Generate file key using the same pattern as upload_documents
        file_key = f"certificates/{uuid.uuid4()}_certificate_{user_application_id}_{certificate_title.replace(' ', '_').lower()}.pdf"

        # Use the exact same S3 upload code as your upload_documents API
        s3_client.put_object(
            Bucket=S3_BUCKET_NAME,
            Key=file_key,
            Body=pdf_content,
            ContentType='application/pdf',
            ACL="public-read"
        )

        # Generate presigned URL using the same pattern as fetch_documents
        presigned_url = s3_client.generate_presigned_url(
            ClientMethod='get_object',
            Params={
                'Bucket': S3_BUCKET_NAME,
                'Key': file_key,
                'ResponseContentType': 'application/pdf',
                'ResponseContentDisposition': 'inline'
            }
        )
        return {
            "file_key": file_key,
            "presigned_url": presigned_url,
            "message": "Certificate generated and uploaded to S3 successfully"
        }

    finally:
        if close_db:
            db.close()

def verify_token(authorization: Optional[str], db: Session):
    if not authorization or not authorization.startswith("Bearer "):
        raise HTTPException(status_code=401, detail="Unauthorized")
    access_token = authorization.split("Bearer ")[1]
    session_exists = db.query(UserSessions).filter(UserSessions.access_token == access_token).first()
    if not session_exists:
        raise HTTPException(status_code=403, detail="Invalid access token")
    return session_exists.user_id

# Function to get user from access token (using user_sessions table)
def get_user_from_token(access_token: str, db: Session):
    session_entry = db.query(UserSessions).filter(UserSessions.access_token == access_token).first()
    return session_entry.user_id if session_entry else None

@app.put("/create-user-applications", response_model=dict)
def create_user_application(
    request: UserApplicationRequest,
    authorization: Optional[str] = Header(None),
    db: Session = Depends(get_db)
):
    user_id=verify_token(authorization, db)

    # Fetch application details using ORM
    mapping_entry = db.query(CouncilApplicationMap).filter_by(id=request.application_type_id).first()
    if not mapping_entry:
        raise HTTPException(status_code=404, detail="Invalid application_type_id")

    application_type = db.query(ApplicationTypes).filter_by(id=mapping_entry.application_id).first()

    # Insert new record using ORM
    new_user_application = UserApplicationDetails(
        user_id=user_id,
        application_id=mapping_entry.application_id,
        stature=1,  # Default Stature
        application_name=request.application_name,
        site_post_code=request.site_post_code,
        site_address=request.site_address,
        site_town_city=request.site_town_city,
        site_county=request.site_county,
        site_easting=request.site_easting,
        site_northing=request.site_northing,
        created_at=datetime.now(UTC)  # Explicitly set created_at only at creation
    )
    db.add(new_user_application)
    db.commit()
    db.refresh(new_user_application)
    
    # Pre-populate site location fields immediately
    site_location_fields = db.query(FieldMaster).filter(FieldMaster.section == 1).all()
    
    for field in site_location_fields:
        field_name_lower = field.name.lower()
        pre_populated_value = None
        
        if field_name_lower == 'postalcode':
            pre_populated_value = request.site_post_code
        elif field_name_lower in ['address line 1']:
            pre_populated_value = request.site_address
        elif field_name_lower == 'town/city':
            pre_populated_value = request.site_town_city
        elif field_name_lower == 'county':
            pre_populated_value = request.site_county
        elif field_name_lower == 'easting(x)':
            pre_populated_value = request.site_easting
        elif field_name_lower == 'northing(y)':
            pre_populated_value = request.site_northing
        
        if pre_populated_value:
            new_user_input = UserInputs(
                user_application_id=new_user_application.id,
                field=field.id,
                user_input=pre_populated_value,
                validation_score=0.0
            )
            db.add(new_user_input)
    db.commit()

    # Trigger notification for draft
    trigger_notification(
        db=db,
        user_id=user_id,
        notification_text="Your application has been created as a draft.",
        status_id=1,
        user_application_id=new_user_application.id
    )

    return {"user_application_id": new_user_application.id}

@app.get("/get-user-applications", response_model=list)
def get_user_applications(
    authorization: str = Header(None),
    db: Session = Depends(get_db)
):
    user_id = verify_token(authorization, db)

    # Fetch user applications from user_application_details table
    applications = db.query(UserApplicationDetails).filter(UserApplicationDetails.user_id == user_id).all()

    if not applications:
        return []
    result = []
    for app in applications:
        status = db.query(UserApplicationStatus).filter(UserApplicationStatus.id == app.stature).first()
        result.append({
            "application_id": app.id,
            "application_name": app.application_name,
            "site_post_code": app.site_post_code,
            "site_address": app.site_address,
            "site_town_city": app.site_town_city,
            "site_county": app.site_county,
            "site_easting": app.site_easting,
            "site_northing": app.site_northing,
            "stature": status.applicant_status if status else None,
            "created_at": app.created_at
        })
    return result

@app.post("/fetch_step_fields")
def fetch_step_fields(request: FetchStepFieldsRequest, authorization: Optional[str] = Header(None), db: Session = Depends(get_db)):
    verify_token(authorization, db)
   
    response_data = {}
   
    # Fetch data for each section
    for section_id in request.section_ids:
        field_ids = [field.id for field in db.query(FieldMaster.id).filter(FieldMaster.section == section_id).all()]
        rows = db.query(UserInputs).filter(UserInputs.user_application_id == request.user_application_id, UserInputs.field.in_(field_ids)).all()
       
        if rows:
            response_data[str(section_id)] = {"data": rows}
        else:
            response_data[str(section_id)] = {"data": []}
   
    # Check for pre-flight data
    pre_flight_record = db.query(PreFlightMaster).filter(
        PreFlightMaster.user_application_id == request.user_application_id
    ).first()
   
    if pre_flight_record:
        response_data["pre-flight"] = {
            "score": pre_flight_record.pre_flight_score,
            "suggested_edits": pre_flight_record.suggested_edits
        }
   
    if not response_data:
        raise HTTPException(status_code=404, detail="No data found")
       
    return response_data

@app.post("/mapped_response")
def mapped_response(request: FetchStepFieldsRequest, authorization: Optional[str] = Header(None), db: Session = Depends(get_db)):
    try:
        # Get raw response from internal function
        raw_response = fetch_step_fields(request, authorization, db)
        
        # Load mapping data
        field_map = get_field_mapping(db)
        app_map = get_application_mapping(db)
        section_map = get_section_mapping(db)

        # Prepare mapped output
        mapped_output = {}
        
        for section_id, section_content in raw_response.items():
            if section_id == "pre-flight":
                suggested_edits = section_content.get("suggested_edits", [])
                edits_mapped = []
                
                for edit in suggested_edits:
                    field_name = edit.get("field_name") or field_map.get(edit.get("field_id"), f"[Field {edit.get('field_id')}]")
                    edits_mapped.append({
                        "field_name": field_name,
                        "user_input": edit.get("user_input"),
                        "suggestion": edit.get("suggestion")
                    })
                mapped_output["pre-flight"] = {"suggested_edits": edits_mapped}
                continue

            section_name = section_map.get(section_id, f"[Section {section_id}]")
            entries = []
            
            for entry in section_content.get("data", []):
                field_id = entry.field
                app_id = entry.user_application_id
                entries.append({
                    "field_name": field_map.get(field_id, f"[Field {field_id}]"),
                    "application_name": app_map.get(app_id, f"[Application {app_id}]"),
                    "user_input": entry.user_input
                })
            mapped_output[section_name] = entries

        return mapped_output

    except HTTPException as http_exc:
        raise http_exc  # Let FastAPI handle already-raised HTTP errors

    except Exception as e:
        # Catch unexpected errors
        raise HTTPException(status_code=500, detail=f"Unexpected error: {str(e)}")

@app.post("/update_step_fields/{user_application_id}/")
def update_step_fields(user_application_id: int, data: UserData, authorization: Optional[str] = Header(None), db: Session = Depends(get_db)):
    verify_token(authorization, db)

    # Check if the user_application_id and field_id already exist
    existing_record = db.query(UserInputs).filter(
        UserInputs.user_application_id == user_application_id,
        UserInputs.field == data.field_id
    ).first()

    if existing_record:
        # If record exists, update it
        existing_record.user_input = data.field_value
        db.commit()
    else:
        # Insert a new record
        new_user_input = UserInputs(
            user_application_id=user_application_id,
            field=data.field_id,
            user_input=data.field_value,
            validation_score=getattr(data, 'validation_score', 0.0)
        )

        db.add(new_user_input)  # Add new record
        db.commit()  # Save changes
        db.refresh(new_user_input)  # Refresh instance to get the ID

    # ✅ Update pre_flight_review.is_updated to True if exists
    pre_flight_record = db.query(PreFlightMaster).filter(
        PreFlightMaster.user_application_id == user_application_id
    ).first()

    if pre_flight_record and not pre_flight_record.is_updated:
        pre_flight_record.is_updated = True
        db.commit()
    
    return {"message": "Fields updated successfully."}

@app.post("/upload_documents")
async def upload_documents(
    files: List[UploadFile] = File(description="PDF files to upload"),
    authorization: Optional[str] = Header(None),
    db: Session = Depends(get_db)
):
    """
    Upload multiple PDF files.
    The files should be sent as form-data with the key 'files'.
    """
    verify_token(authorization, db)
    
    if not files:
        raise HTTPException(status_code=400, detail="No files provided")
    
    file_keys = []
    for file in files:
        if not file.filename.lower().endswith('.pdf'):
            raise HTTPException(status_code=400, detail=f"File {file.filename} is not a PDF")
            
        file_key = f"uploads/{uuid.uuid4()}_{file.filename}"
        try:
            file_content = await file.read()
            s3_client.put_object(
                Bucket=S3_BUCKET_NAME,
                Key=file_key,
                Body=file_content,
                ContentType='application/pdf',
                ACL="public-read"
            )
            file_keys.append({"file_key": file_key})
        except ClientError as e:
            raise HTTPException(status_code=500, detail=str(e))
        finally:
            await file.close()
    
    return file_keys

@app.delete("/delete_documents")
def delete_documents(file_key: str, authorization: Optional[str] = Header(None), db: Session = Depends(get_db)):
    verify_token(authorization, db)
    try:
        s3_client.delete_object(Bucket=S3_BUCKET_NAME, Key=file_key)
        return {"message": "File deleted successfully."}
    except ClientError as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/fetch_documents")
def fetch_documents(file_key: str, authorization: Optional[str] = Header(None), db: Session = Depends(get_db)):
    verify_token(authorization, db)
    try:
        # Determine content type based on file extension
        if file_key.lower().endswith('.png'):
            content_type = 'image/png'
        elif file_key.lower().endswith('.pdf'):
            content_type = 'application/pdf'
        else:
            # Default to PDF for backward compatibility
            content_type = 'application/pdf'
        
        url = s3_client.generate_presigned_url(
            ClientMethod='get_object',
            Params={
                'Bucket': S3_BUCKET_NAME,
                'Key': file_key,
                'ResponseContentType': content_type,
                'ResponseContentDisposition': 'inline'
            },
            ExpiresIn=3600  # 1 hour expiration
        )
        return {"presigned_url": url}
    except ClientError as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/pre-flight")
async def validate_fields(request: UserApplicationID, authorization: Optional[str] = Header(None), db: Session = Depends(get_db)):

    # Verify the tokens
    user_id = verify_token(authorization, db)
    # Check the user_application_id for the user is valid or not
    user_app = db.query(UserApplicationDetails).filter_by(
        id=request.user_application_id,
        user_id=user_id
    ).first()
    if not user_app:
        raise HTTPException(status_code=403, detail="Unauthorized access to the application.")

    # Check if a pre-flight record already exists
    existing_review = db.query(PreFlightMaster).filter(
        PreFlightMaster.user_application_id == request.user_application_id
    ).first()

    # If exists and is_updated == False, return stored results
    if existing_review and not existing_review.is_updated:
        return {
            "overall_score": existing_review.pre_flight_score.get("overall_score"),
            **{f"pre_flight_score_type_{t}": v for t, v in existing_review.pre_flight_score.items() if t != "overall_score"},
            "incorrect_fields": existing_review.suggested_edits or []
        }
    
    # --- NEW: fetch *all* requested fields ---
    all_fields = (
        db.query(FieldMaster)
            .join(ApplicationTypes, FieldMaster.application_type == ApplicationTypes.id)
            .join(CouncilApplicationMap, ApplicationTypes.id == CouncilApplicationMap.application_id)
            .join(UserApplicationDetails, CouncilApplicationMap.id == UserApplicationDetails.application_id)
            .filter(UserApplicationDetails.id == request.user_application_id)
            .order_by(FieldMaster.type, FieldMaster.id)  # Optional: for consistent ordering
            .all()
    )

    user_inputs = (
        db.query(UserInputs)
          .filter(UserInputs.user_application_id == request.user_application_id)
          .all()
    )
    user_input_map = {ui.field: ui for ui in user_inputs}

    fields_to_validate = []
    for field in all_fields:
        if field.is_optional == 0:  # Always include mandatory fields
            fields_to_validate.append(field)
        elif field.is_optional == 1 and field.id in user_input_map:  # Include optional fields only if they have input
            fields_to_validate.append(field)

    if not fields_to_validate:
        raise HTTPException(
            status_code=404,
            detail="No valid fields found for validation"
        )

    # Get application type information for validation
    application_type_info = db.query(ApplicationTypes).join(
        CouncilApplicationMap, ApplicationTypes.id == CouncilApplicationMap.application_id
    ).join(
        UserApplicationDetails, CouncilApplicationMap.id == UserApplicationDetails.application_id
    ).filter(
        UserApplicationDetails.id == request.user_application_id
    ).first()
    
    application_type_name = application_type_info.kind if application_type_info else "Unknown"
    
    # 2. Group inputs by type
    grouped_by_type: dict[int, list[dict]] = {}
    for field in fields_to_validate:
        user_input = user_input_map.get(field.id)
        if user_input:
            grouped_by_type.setdefault(field.type, []).append({
                "field_id": field.id,
                "field_name": field.name,
                "user_input": user_input.user_input,
                "field_weight": field.weightage
            })
    
    # 3. Prepare containers
    incorrect_fields: list[dict] = []
    validation_map: dict[int, int] = {}
    pre_flight_scores: dict[int, float] = {}
    # DEBUG container—remove this block later
    calculations: dict = {
        "type_calculations": {},
        "overall_calculation": {}
    }

    document_inputs = []
    if 3 in grouped_by_type:
        for entry in grouped_by_type[3]:
            if entry["field_id"] == 165:
                try:
                    # Handle the file path from the database
                    file_path = entry["user_input"]
                    if not file_path:
                        continue  # Skip if no file path provided
                        
                    if isinstance(file_path, str):
                        try:
                            # Parse the JSON string if it's stored as a string
                            file_path = json.loads(file_path)
                        except json.JSONDecodeError:
                            # If not JSON, use as is
                            pass
                            
                    # Get all file paths from the dictionary
                    if isinstance(file_path, dict):
                        if not file_path:  # Check if dictionary is empty
                            continue
                        # Add all values from the dictionary instead of just the first one
                        document_inputs.extend(file_path.values())
                    else:
                        # If it's a single file path (string), add it directly
                        document_inputs.append(file_path)
                except Exception as e:
                    # Log the error but continue processing other entries
                    print(f"Error processing file path for field 165: {str(e)}")
                    continue
    # If no valid document inputs found, return early with appropriate message
    if not document_inputs:
        field_165 = next((f for f in all_fields if f.id == 165), None)
        return {
            "overall_score": 0,
            "pre_flight_score_type_3": 0,
            "incorrect_fields": [{
                "section_id": field_165.section if field_165 else None,
                "field_id": 165,
                "field_name": field_165.name if field_165 else "Document Upload",
                "user_input": "No documents found",
                "suggestion": "Please upload at least one valid document"
            }],
            "calculations": {
                "type_calculations": {},
                "overall_calculation": {
                    "overall_weighted_sum": 0,
                    "overall_total_weight": 0,
                    "overall_score": 0
                }
            }
        }
 
    document_request = DocumentValidationRequest(file_keys=document_inputs)
    validation_result = await validate_document(document_request, authorization, db)
 
    # Collect checklist and final_score for each document
    document_checklists = validation_result["checklist"]
    document_final_scores = validation_result["final_score"]

    # Calculate validation score for field 165 using final_score from /validate_document
    if document_checklists:
        field_165 = next((f for f in all_fields if f.id == 165), None)
        if field_165:
 
            # Add to validation_map for pre-flight score calculation
            validation_map[165] = document_final_scores
 
            # Update the validation score in UserInputs table
            user_input_record = db.query(UserInputs).filter(
                UserInputs.user_application_id == request.user_application_id,
                UserInputs.field == 165
            ).first()
 
            if user_input_record:
                user_input_record.validation_score = document_final_scores
                db.commit()
           
            # Get field 165 details from database
            field_165_details = db.query(FieldMaster).filter(FieldMaster.id == 165).first()
 
            # Add document checklist suggestions to incorrect_fields
            if field_165_details:
                document_suggestions = {}
                document_suggestions["segmentation"] = document_checklists.get("segmentation", {}).get("edit", [])
                for doc in document_checklists.get("ocr_orientation", []):
                    document_suggestions[doc["file_key"]] = doc.get("edit", [])
                incorrect_fields.append({
                    "section_id": field_165_details.section,
                    "field_id": 165,
                    "field_name": field_165_details.name,
                    "user_input": document_inputs,
                    "suggestion": document_suggestions
                })
    MAX_ENTRIES_PER_CHUNK = 30  # Tune this based on token limits and entry length

    # 4. Validate each group via LLM with chunking
    for t_id, entries in grouped_by_type.items():
        num_chunks = ceil(len(entries) / MAX_ENTRIES_PER_CHUNK)

        chunked_results = []

        # 4. Validate each group via LLM
        for i in range(num_chunks):

            chunk_entries = entries[i * MAX_ENTRIES_PER_CHUNK : (i + 1) * MAX_ENTRIES_PER_CHUNK]
            chunk_entries = [entry for entry in chunk_entries if entry["field_id"] != 165]
            # 1) Build your system + user messages
            system_prompt = (
                "You are a planning application validator with common sense. Your role is to identify genuine problems, not to be overly strict.\n\n"
                
                "CORE PRINCIPLE: Only reject fields that are clearly wrong or problematic. Accept reasonable variations and partial information.\n\n"
                
                "CRITICAL: Always validate field content against the application type. A mismatch between application type and field content is a major validation error.\n\n"
                
                "VALIDATION RULES:\n"
                "**SCORE 1 (VALID) IF**:\n"
                "- Contains any reasonable information\n"
                "- Format makes sense for the field type\n"
                "- Not obviously fake or test data\n"
                "- Partial information is acceptable\n\n"
                
                "**SCORE 0 (INVALID) ONLY IF**:\n"
                "- Clearly test data: 'test', 'tttt', 'hhhh', '123', 'abc', 'qwerty'\n"
                "- Completely empty or just spaces\n"
                "- Random characters with no meaning\n"
                "- Obviously fake: '<EMAIL>', '0000000000'\n\n"
                
                "SPECIFIC FIELD RULES:\n"
                "**Title**:\n"
                "- Accept: 'Mr.','Mr','Mrs.','Mrs','Miss','Ms','Ms.','Mx','Mx.','Master'\n"
                "- Reject: 'M', blanks, everything else other than Accepted\n\n"
                
                "**Names**:\n"
                "- Accept: First name only, last name only, full name, initials\n"
                "- Accept: Common names, unusual names, international names\n"
                "- Accept: Names with hyphens, apostrophes, spaces\n"
                "- Reject: 'test', 'tttt', 'hhhh', '123', 'abc'\n\n"
                
                "**Addresses**:\n"
                "- Accept: Street name only, house number only, partial addresses\n"
                "- Accept: '123 Main St', 'Main Street', 'Building A', 'Unit 5'\n"
                "- Accept: Short addresses, long addresses, any reasonable format\n"
                "- Accept: Abbreviations: St, Rd, Ave, Blvd, Dr, etc.\n"
                "- Reject: 'test address', 'fake street', '123 fake'\n\n"
                
                "**Postcodes**:\n"
                "- Accept: Any reasonable postcode format\n"
                "- Accept: UK, US, international formats\n"
                "- Accept: Partial postcodes if they make sense\n"
                "- Reject: '00000', '12345', 'test'\n\n"
                
                "**Dates**:\n"
                "- Accept: DD/MM/YYYY, DD-MM-YYYY, MM/DD/YYYY, YYYY-MM-DD\n"
                "- Accept: Any reasonable date format\n"
                "- Accept: Future dates, past dates, current dates\n"
                "- Reject: 'test date', '00/00/0000', 'invalid'\n\n"
                
                "**Phone Numbers**:\n"
                "- Accept: UK numbers starting with 0 (e.g., '07123456789', '02012345678')\n"
                "- Accept: UK numbers with +44 country code (e.g., '+447123456789', '+442012345678')\n"
                "- Accept: UK numbers with 44 country code without + (e.g., '447123456789', '442012345678')\n"
                "- Accept: International numbers with + country codes (e.g., '+**************', '+33 1 23 45 67 89')\n"
                "- Accept: International numbers with country codes without + (e.g., '**************', '33 1 23 45 67 89')\n"
                "- Accept: Numbers with spaces, dashes, parentheses (e.g., '************', '020-1234-5678')\n"
                "- Accept: Landline and mobile numbers\n"
                "- Reject: '0000000000', '1234567890', 'test', '***********', '***********'\n"
                "- Reject: Numbers that don't start with 0 (UK), + (international), or country code\n\n"
                
                "**Email Addresses**:\n"
                "- Accept: Any reasonable email format\n"
                "- Accept: Personal, business, international domains\n"
                "- Accept: With/without subdomains\n"
                "- Reject: '<EMAIL>', '<EMAIL>', '<EMAIL>'\n\n"
                
                "**Descriptions/Text**:\n"
                "- Accept: Short descriptions, long descriptions\n"
                "- Accept: Technical terms, abbreviations, jargon\n"
                "- Accept: Partial information, bullet points\n"
                "- Accept: Any reasonable text that makes sense\n"
                "- Reject: 'test description', 'lorem ipsum', random strings\n\n"
                
                "**Numbers/Measurements**:\n"
                "- Accept: Any reasonable numerical value\n"
                "- Accept: With/without units, decimals, fractions\n"
                "- Accept: Small numbers, large numbers\n"
                "- Reject: '0' (unless it's a valid measurement), '999999'\n\n"
                
                "**JSON Fields**:\n"
                "- Validate each key-value pair individually\n"
                "- Accept reasonable key names and values\n"
                "- Reject only if ALL pairs are invalid\n"
                "- Partial information in JSON is acceptable\n\n"
                
                "**APPLICATION TYPE VALIDATION**:\n"
                "CRITICAL: Check if field content matches application type:\n"
                "- HOUSEHOLDER APPLICATION: Must be domestic/residential work only\n"
                "  * Accept: extensions, conservatories, garages, sheds, garden rooms, domestic alterations, home improvements\n"
                "  * Reject: commercial developments, shops, offices, industrial buildings, car parks, multi-unit developments\n"
                "  * Reject: 'shopping space', 'shops', 'car park', 'office building', 'industrial', 'commercial', 'retail', 'business', 'multi-unit', 'warehouse', 'factory', 'hotel', 'restaurant', 'pub', 'bar', 'gym', 'clinic', 'school', 'church', 'community center'\n"
                "- FULL PLANNING APPLICATION: Can include commercial, residential, or mixed-use\n"
                "- OUTLINE APPLICATION: Can include any development type\n"
                "- LISTED BUILDING CONSENT: Must be for listed building alterations\n"
                "- CHANGE OF USE: Must describe change from one use to another\n\n"
                
                "**APPLICATION TYPE VALIDATION RULES**:\n"
                "CRITICAL: Validate ALL fields against the application type. Check any field that contains:\n"
                "- Development descriptions, proposed uses, building types, site details\n"
                "- Work descriptions, project details, use classifications\n"
                "- Any content that describes what will be built or how the site will be used\n"
                "- Field names that suggest development content (description, use, type, details, etc.)\n\n"
                
                "**IMPORTANT GUIDELINES**:\n"
                "- Be lenient with partial information\n"
                "- Accept reasonable variations and formats\n"
                "- Don't expect complete information in every field\n"
                "- Focus on identifying genuine problems, not perfection\n"
                "- Consider that users might enter information in stages\n"
                "- Accept abbreviations and common shortcuts\n"
                "- Don't reject fields just because they're short or incomplete\n"
                "- For UK phone numbers: Must start with 0, +44, or 44\n"
                "- For international numbers: Must start with + or country code\n"
                "- CRITICAL: Validate description of work against application type\n\n"
                
                "**SUGGESTIONS**:\n"
                "- Only suggest improvements for clearly problematic fields\n"
                "- Be helpful, not critical\n"
                "- Provide practical examples\n"
                "- Don't suggest changes for acceptable variations\n\n"
                
                "**RESPONSE FORMAT**:\n"
                "Respond ONLY with a JSON object in this exact format:\n"
                '{ "results": [ { "field_id": <number>, "validation_score": 1 or 0, "suggestion": "<helpful explanation>" }, ... ] }\n\n'
                
                "**REMEMBER**: Your job is to help users, not to be a perfectionist. Accept reasonable input and only reject clearly problematic data."
            )

            def format_user_input(user_input):
                if isinstance(user_input, str) and user_input.strip().startswith("{"):
                    return user_input  # Return JSON as is
                return f'"{user_input}"'  # Wrap non-JSON in quotes

            user_entries = "\n".join(
                f'{i+1}. field_id: {e["field_id"]}, '
                f'field_name: "{e["field_name"]}", '
                f'user_input: {format_user_input(e["user_input"])}'
                for i, e in enumerate(chunk_entries)
            )

            user_prompt = (
                f"Application Type: {application_type_name}\n\n"
                f"Entries to validate:\n{user_entries}\n\n"
                "CRITICAL VALIDATION RULES:\n"
                f"1. APPLICATION TYPE VALIDATION (for ALL fields):\n"
                f"   - Current Application Type: {application_type_name}\n"
                "   - Check ALL fields for content that matches or conflicts with the application type\n"
                "   - Look for development descriptions, proposed uses, building types, work details\n"
                "   - Validate field content against application type requirements\n\n"
                "2. APPLICATION TYPE SPECIFIC RULES:\n"
                "   - HOUSEHOLDER APPLICATION: Must be domestic/residential work only\n"
                "     * Reject: commercial terms (shops, office, industrial, car park, retail, business, warehouse, factory, hotel, restaurant, pub, bar, gym, clinic, school, church, community center, multi-unit, commercial development)\n"
                "     * Accept: domestic terms (extension, conservatory, garage, shed, garden room, domestic alterations, home improvement, residential, house, bungalow, flat, apartment, dwelling)\n"
                "   - FULL PLANNING APPLICATION: Can include commercial, residential, or mixed-use\n"
                "   - OUTLINE APPLICATION: Can include any development type\n"
                "   - LISTED BUILDING CONSENT: Must be for listed building alterations\n"
                "   - CHANGE OF USE: Must describe change from one use to another\n"
                "   - OTHER APPLICATION TYPES: Validate based on their specific requirements\n\n"
                "3. For all other fields: Use standard validation rules\n\n"
                "IMPORTANT: Respond *only* with a JSON object in this format:\n"
                '{ "results": [ { "field_id": <num>, '
                '"validation_score": 1 or 0, "suggestion": "<string>" }, ... ] }'
            )

            # 2) Call the Converse API correctly
            resp = bedrock_client.converse(
                modelId=MODEL_ID,                             # your model
                system=[{"text": system_prompt}],             # system instruction
                messages=[{"role": "user", "content": [{"text": user_prompt}]}],
                inferenceConfig={                             # inference params
                    "maxTokens":   2048,
                    "temperature": 0.2,
                    "topP":        0.9
                }
            )

            # 3) Extract the assistant's reply
            # with bedrock-runtime, resp is already a dict
            # Bedrock chat models return something like:
            # { "output":[{ "message":{ "content":"<the text>" }, ... }], ... }
            try:
                model_output = resp["output"]["message"]["content"][0]["text"]
            except (KeyError, IndexError):
                raise HTTPException(
                    status_code=502,
                    detail=f"Unexpected LLM response shape for type {t_id}: {resp}"
                )
            # DEBUG: print or log the raw output so you can inspect what the LLM actually returned
            # print(f"[DEBUG] Raw LLM output for type {t_id}:\n{model_output}\n")

            # Extract JSON substring
            raw = model_output.strip()
            # print(f"raw == {raw}")
            # If they wrapped it in triple backticks, remove them
            json_block = raw
            if raw.startswith("```"):
                # drop the first and last lines of backticks
                lines = raw.splitlines()
                # print(lines)
                # find the line with ```json
                start_idx = next((i for i,l in enumerate(lines) if l.strip().startswith("```json")), None)
                # find the last ``` 
                end_idx = next((i for i,l in enumerate(lines) if l.strip() == "```" and i>start_idx), None)
                if start_idx is None or end_idx is None:
                    raise HTTPException(
                        status_code=502,
                        detail=(
                            f"Could not locate JSON fences in LLM output for type {t_id}:\n"
                            f"{model_output!r}"
                        )
                    )
                json_block = "\n".join(lines[start_idx+1 : end_idx])


            # find the JSON braces
            o = json_block.find("{")
            c = json_block.rfind("}")
            if o == -1 or c == -1 or o > c:
                raise HTTPException(
                    status_code=502,
                    detail=(
                        f"LLM output did not contain a valid JSON object for type {t_id}.\n"
                        f"Here's what we got:\n{json_block[:300]!r}"
                    )
                )
            json_str = json_block[o : c+1]

            # Parse it
            try:
                result_data = json.loads(json_str)
            except json.JSONDecodeError as e:
                raise HTTPException(
                    status_code=502,
                    detail=(
                        f"JSON parse error: {e.msg}\n"
                        f"JSON snippet:\n{json_str}"
                    )
                )
            chunked_results.extend(result_data.get("results", []))
        

        # 5. Process LLM results
        for r in chunked_results:
            fid = r.get("field_id")
            score = 1 if r.get("validation_score") == 1 else 0
            validation_map[fid] = score

            # Update DB record
            user_input = user_input_map.get(fid)
            if user_input:
                user_input.validation_score = float(score)

            if score == 0:
                fld = next(f for f in fields_to_validate if f.id == fid)
                incorrect_fields.append({
                    "section_id": fld.section,
                    "field_id": fid,
                    "field_name": fld.name,
                    "user_input": user_input.user_input if user_input else "",
                    "suggestion": r.get("suggestion", "")
                })

        # After you load field_records from FieldMaster.filter(id.in_(request.field_ids)):
        type_total_weight: dict[int, float] = {}
        for f in fields_to_validate:
            type_total_weight.setdefault(f.type, 0.0)
            type_total_weight[f.type] += f.weightage

        # 6. Compute this type's pre-flight score
        for t_id, entries in grouped_by_type.items(): # Added loop to include every field in the calculation for single type.
            # total_w = sum(e["field_weight"] for e in entries) # Only for the fields present in the user_inputs table.
            total_w = type_total_weight.get(t_id, 0.0) # For all the fields present in the fields table for one type.
            weighted_sum = sum(
                e["field_weight"] * validation_map.get(e["field_id"], 0)
                for e in entries
            )
            score = (weighted_sum / total_w) if total_w else 0.0
            pre_flight_scores[t_id] = score
            # TEMP: record the raw numbers
            calculations["type_calculations"][t_id] = {
                "weighted_sum":       weighted_sum,
                "total_field_weight": total_w,
                "pre_flight_score":   score
            }


    # 7. Compute overall score
    type_records = db.query(TypeMaster).filter(TypeMaster.id.in_(pre_flight_scores.keys())).all()
    type_weight_map = {t.id: t.weightage for t in type_records}
    num = sum(type_weight_map[t] * pre_flight_scores[t] for t in pre_flight_scores)
    den = sum(type_weight_map[t] for t in pre_flight_scores)
    overall_score = round((num / den)*100) if den else 0.0

    # TEMP: record the overall calculation
    calculations["overall_calculation"] = {
        "overall_weighted_sum": num,
        "overall_total_weight": den,
        "overall_score": overall_score
    }
    
    # Insert or update pre_flight_review table
    pre_flight_score_json = {
        "overall_score": overall_score,
        **{f"{t}": s for t, s in pre_flight_scores.items()}
    }

    # Insert new record if not present
    if not existing_review:
        review_entry = PreFlightMaster(
            user_application_id=request.user_application_id,
            pre_flight_score=pre_flight_score_json,
            suggested_edits=incorrect_fields,
            is_updated=False
        )
        db.add(review_entry)
        user_app.stature = 3
        db.commit()
    else:
        # Update the existing record if is_updated is True
        existing_review.pre_flight_score = pre_flight_score_json
        existing_review.suggested_edits = incorrect_fields
        existing_review.is_updated = False  # Reset status
        user_app.stature = 3
        db.commit()
    
    application_status = db.query(
            UserApplicationDetails.stature,
            UserApplicationStatus.applicant_status
        ).join(
            UserApplicationStatus, UserApplicationDetails.stature == UserApplicationStatus.id
        ).filter(
            UserApplicationDetails.id == request.user_application_id
        ).first()
    # Convert to dictionary format:
    application_status_dict = {
        "status_id": application_status.stature,
        "status": application_status.applicant_status
    } if application_status else None
    # 7) Build response
    response = {
        "overall_score": overall_score,
        **{f"pre_flight_score_type_{t}": s for t, s in pre_flight_scores.items()},
        "incorrect_fields": incorrect_fields,
        "calculations":calculations,
        "application_status":application_status_dict
    }

    return response

@app.post("/comments")
def add_comments(request: CommentsRequest, authorization: Optional[str] = Header(None), db: Session = Depends(get_db)):
    verify_token(authorization, db)

    user_application = db.query(PreFlightMaster).filter_by(user_application_id=request.user_application_id).first()
    if not user_application:
        raise HTTPException(status_code=404, detail="User application not found")

    user_application.comments = request.comment
    db.commit()
    return {"message": "Comment added successfully"}

@app.post("/validate_document")
async def validate_document(request: DocumentValidationRequest, authorization: Optional[str] = Header(None), db=Depends(get_db)):
    verify_token(authorization, db)
    result = validate_multiple(request.file_keys)
    if result.get("status") == "error":
        from utils.segmentation import CHECK
        return {
            "ocr_orientation_score": 0,
            "segmentation_score": 0,
            "final_score": 0,
            "checklist": {
                "ocr_orientation": [],
                "segmentation": {"score": 0, "edit": ["Validation error: " + result.get("message", "Unknown error")]}
            }
        }

    # --- OCR & Orientation ---
    individual_validations = result.get("individual_validations", [])
    ocr_orientation_list = []
    ocr_scores = []
    for file in individual_validations:
        score = file.get("score", 0)
        file_key = file.get("file_key")
    
        ocr_orientation_suggestions = file.get("checklist", {}).get("suggestions", [])
        ocr_orientation_list.append({
            "file_key": file_key,
            "score": score,
            "edit": ocr_orientation_suggestions
        })
        ocr_scores.append(score)

    max_ocr_score = 11
    num_files = len(ocr_scores)
    ocr_orientation_score = (sum(ocr_scores) / (num_files * max_ocr_score)) if num_files else 0

    # --- Segmentation ---
    collective_segmentation = result.get("collective_segmentation", {})
    seg_report = collective_segmentation.get("validation_report", {})
    # count "FOUND" as 1, "MISSING" as 0, sum for score
    seg_checklist = seg_report.get("checklist_validation", {})
    seg_score_raw = sum(3 for v in seg_checklist.values() if "FOUND" in v)
    max_seg_score = 18
    segmentation_score = seg_score_raw / max_seg_score if max_seg_score else 0

    final_score = (ocr_orientation_score + segmentation_score) / 2

    checklist = {
        "ocr_orientation": ocr_orientation_list,
        "segmentation": {
            "score": seg_score_raw,
            "edit": [k for k, v in seg_checklist.items() if "MISSING" in v]
        }
    }

    output = {
        "ocr_orientation_score": round(ocr_orientation_score, 3),
        "segmentation_score": round(segmentation_score, 3),
        "final_score": round(final_score, 3),
        "checklist": checklist
    }
    return output

@app.post("/preview_mode", response_model=PDFGenerationResponse)
def preview_mode(
    request: MappedResponseRequest,
    authorization: Optional[str] = Header(None),
    db: Session = Depends(get_db)
):
    user_id = verify_token(authorization, db)
    user_role = db.query(Users.role).filter(Users.id == user_id).scalar()

    # Fetch pre-flight record for this application ID only
    pre_flight = db.query(PreFlightMaster).filter(
        PreFlightMaster.user_application_id == request.user_application_id
    ).first()

    # If user is admin or planner, just return the existing preview_doc (if any)
    if user_role in ("admin", "planner"):
        if pre_flight and pre_flight.preview_doc:
            return {"file_key": pre_flight.preview_doc}
        else:
            raise HTTPException(status_code=404, detail="Preview document not found.")

    temp_dir = tempfile.mkdtemp()
    attached_pdfs = []

    def draw_disclaimer(canvas, x, y, max_width):
        text = "*This is a reference copy of the application. Not valid for submission to Local Planning Authority"
        canvas.setFillColor(colors.red)
        canvas.setFont("Helvetica", 8)
        words = text.split()
        lines, line = [], ""
        for word in words:
            test_line = f"{line} {word}".strip()
            if canvas.stringWidth(test_line, "Helvetica", 8) <= max_width:
                line = test_line
            else:
                lines.append(line)
                line = word
        if line:
            lines.append(line)
        for i, line in enumerate(lines):
            canvas.drawRightString(x, y - (i * 10), line)

    def draw_first_page(canvas, doc):
        # --- Draw watermark ---
        current_dir = os.path.dirname(os.path.abspath(__file__))
        watermark_path = os.path.join(current_dir, "watermark", "watermark.png")
        if os.path.exists(watermark_path):
            try:
                watermark = ImageReader(watermark_path)            
                # Draw watermark at center with some transparency
                canvas.saveState()
                # canvas.translate(A4[0] / 2, A4[1] / 2)
                page_width, page_height = A4
                # canvas.rotate(30)
                # canvas.setFillAlpha(0.15)  # semi-transparent
                canvas.drawImage(watermark, 0, 0, width=page_width, height=page_height, mask='auto')
                canvas.restoreState()
            except Exception as e:
                print(f"Error drawing watermark: {e}")

        # --- Draw Paive logo instead of text ---
        logo_path = os.path.join(current_dir, "logo", "paive_logo.png")
        if os.path.exists(logo_path):
            try:
                logo = ImageReader(logo_path)
                canvas.drawImage(logo, 30, A4[1] - 80, width=120, height=40, mask='auto')
            except Exception as e:
                print(f"Error drawing logo: {e}")

        now = datetime.now().strftime("%d %B %Y, %I:%M %p")
        canvas.setFont("Helvetica", 10)
        canvas.setFillColor(colors.black)
        canvas.drawString(30, A4[1] - 100, f"{now}")
        draw_disclaimer(canvas, A4[0] - 30, A4[1] - 30, max_width=180)
        canvas.setFillColor(colors.HexColor("#3EB489"))
        canvas.rect(0, 0, A4[0], 10, fill=1, stroke=0)

    def draw_other_pages(canvas, doc):
        # --- Draw watermark ---
        current_dir = os.path.dirname(os.path.abspath(__file__))
        watermark_path = os.path.join(current_dir, "watermark", "watermark.png")
        if os.path.exists(watermark_path):
            try:
                watermark = ImageReader(watermark_path)
                canvas.saveState()
                # canvas.translate(A4[0] / 2, A4[1] / 2)
                page_width, page_height = A4
                # canvas.rotate(30)
                # canvas.setFillAlpha(0.15)  # semi-transparent
                canvas.drawImage(watermark, 0, 0, width=page_width, height=page_height, mask='auto')
                canvas.restoreState()
            except Exception as e:
                print(f"Error drawing watermark: {e}")

        draw_disclaimer(canvas, A4[0] - 30, A4[1] - 30, max_width=180)
        canvas.setFillColor(colors.HexColor("#3EB489"))
        canvas.rect(0, 0, A4[0], 10, fill=1, stroke=0)

    def get_presigned_url(file_key: str) -> Optional[str]:
        try:
            return s3_client.generate_presigned_url(
                ClientMethod='get_object',
                Params={'Bucket': S3_BUCKET_NAME, 'Key': file_key},
                ExpiresIn=3600
            )
        except ClientError:
            return None

    try:
        # Fetch pre-flight record for this application ID only
        pre_flight = db.query(PreFlightMaster).filter(
            PreFlightMaster.user_application_id == request.user_application_id
        ).first()

        if not pre_flight:
            raise HTTPException(status_code=404, detail="Pre-flight entry not found")

        existing_file_key = getattr(pre_flight, 'preview_doc', None)
    
        
        # Delete existing file from S3 if applicable
        if existing_file_key:
            try:
                s3_client.delete_object(Bucket=S3_BUCKET_NAME, Key=existing_file_key)
            except ClientError as e:
                raise HTTPException(status_code=500, detail=f"Failed to delete previous preview: {str(e)}")

        # Proceed with generating the new PDF
        data = mapped_response(request, authorization, db)
        styles = getSampleStyleSheet()
        style_normal = styles["Normal"]
        style_heading = styles["Heading2"]
        elements = []

        doc_sections = {"Upload Documents", "Plans and Drawings", "Location Plan"}
        main_pdf_path = os.path.join(temp_dir, "main.pdf")
        final_pdf_path = os.path.join(temp_dir, "final.pdf")

        doc = SimpleDocTemplate(main_pdf_path, pagesize=A4, rightMargin=30, leftMargin=30, topMargin=30, bottomMargin=30)
        elements.append(Spacer(1, 60))
        application_name = next((section[0].get("application_name", "Application Summary")
                                 for section in data.values() if isinstance(section, list) and section), "Application Summary")
        elements.append(Paragraph(application_name, styles["Title"]))
        elements.append(Spacer(1, 40))

        for section_title, section_data in data.items():
            if section_title == "pre-flight":
                continue

            elements.append(Spacer(1, 24))
            elements.append(Paragraph(f"<b>{section_title}</b>", style_heading))
            table_data = []
            is_doc_section = section_title.strip().lower() in {s.lower() for s in doc_sections}

            for item in section_data:
                field = item.get("field_name", "")
                value = item.get("user_input", "")

                if is_doc_section and isinstance(value, str):
                    try:
                        file_dict = json.loads(value)
                        if not isinstance(file_dict, dict):
                            raise ValueError("Expected dict of files")

                        file_links = []
                        for filename, path in file_dict.items():
                            cleaned_path = path.split("uploads/")[-1]
                            file_key = f"uploads/{cleaned_path}"
                            url = get_presigned_url(file_key)

                            if url:
                                response = requests.get(url)
                                if response.status_code == 200:
                                    safe_field = field.replace(" ", "_").lower()
                                    pdf_filename = f"{safe_field}_{filename}".replace(" ", "_")
                                    pdf_path = os.path.join(temp_dir, pdf_filename)
                                    with open(pdf_path, "wb") as f:
                                        f.write(response.content)
                                    attached_pdfs.append((field, filename, pdf_path))
                                    file_links.append(filename)
                                else:
                                    file_links.append(f"{filename} (Download failed)")
                            else:
                                file_links.append(f"{filename} (Invalid URL)")

                        value_display = "<br/>".join(file_links)
                        table_data.append([Paragraph(field, style_normal), Paragraph(value_display, style_normal)])
                    except Exception as e:
                        table_data.append([Paragraph(field, style_normal), Paragraph(f"Error processing documents: {str(e)}", style_normal)])
                else:
                    try:
                        parsed = json.loads(value)
                        if isinstance(parsed, dict):
                            for sub_key, sub_val in parsed.items():
                                label = f"{field} - {sub_key}" if field else sub_key
                                table_data.append([Paragraph(label, style_normal), Paragraph(str(sub_val), style_normal)])
                        else:
                            table_data.append([Paragraph(field, style_normal), Paragraph(str(parsed), style_normal)])
                    except Exception:
                        table_data.append([Paragraph(field, style_normal), Paragraph(str(value), style_normal)])

            table = Table(table_data, colWidths=[180, 330])
            table.setStyle(TableStyle([
                ('BOX', (0, 0), (-1, -1), 1, colors.black),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ]))
            elements.append(KeepTogether([Spacer(1, 6), table]))
            elements.append(Spacer(1, 12))

        doc.build(elements, onFirstPage=draw_first_page, onLaterPages=draw_other_pages)

        merger = PdfMerger()
        merger.append(main_pdf_path)

        for section_title, filename, pdf_path in attached_pdfs:
            title_page = os.path.join(temp_dir, f"{uuid.uuid4()}.pdf")
            c = canvas.Canvas(title_page, pagesize=A4)

            # --- Draw watermark on the title page ---
            current_dir = os.path.dirname(os.path.abspath(__file__))
            watermark_path = os.path.join(current_dir, "watermark", "watermark.png")
            if os.path.exists(watermark_path):
                try:
                    watermark = ImageReader(watermark_path)
                    page_width, page_height = A4
                    c.saveState()
                    c.drawImage(watermark, 0, 0, width=page_width, height=page_height, mask='auto')
                    c.restoreState()
                except Exception as e:
                    print(f"Error drawing watermark on title page: {e}")

            c.setFont("Helvetica-Bold", 16)
            c.drawString(72, 800, f"{section_title}: {filename}")
            draw_disclaimer(c, A4[0] - 30, A4[1] - 30, max_width=180)
            c.setFillColor(colors.HexColor("#3EB489"))
            c.rect(0, 0, A4[0], 10, fill=1, stroke=0)
            c.save()
            merger.append(title_page)
            merger.append(pdf_path)

        merger.write(final_pdf_path)
        merger.close()

        s3_key = f"generated_pdfs/application_{request.user_application_id}_{datetime.utcnow().strftime('%Y%m%d%H%M%S')}.pdf"
        with open(final_pdf_path, "rb") as f:
            s3_client.upload_fileobj(f, S3_BUCKET_NAME, s3_key, ExtraArgs={"ACL": "public-read"})

        # Update only this application's row
        db.query(PreFlightMaster).filter(
            PreFlightMaster.user_application_id == request.user_application_id
        ).update({"preview_doc": s3_key})
        db.commit()

        return {"file_key": s3_key}

    finally:
        shutil.rmtree(temp_dir, ignore_errors=True)
 
@app.get("/fetch_all_applications")
def fetch_all_applications(authorization: Optional[str] = Header(None), db: Session = Depends(get_db)):
    user_id = verify_token(authorization, db)
    user_role = db.query(Users.role).filter(Users.id == user_id).scalar()
 
    if not user_role:
        raise HTTPException(status_code=401, detail="Role not found")

    # Base query with join to UserApplicationStatus, CouncilApplicationMap, ApplicationTypes, and CouncilDetails
    base_query = db.query(UserApplicationDetails, UserApplicationStatus, CouncilApplicationMap, ApplicationTypes, CouncilDetails)\
        .join(UserApplicationStatus, UserApplicationDetails.stature == UserApplicationStatus.id)\
        .join(CouncilApplicationMap, UserApplicationDetails.application_id == CouncilApplicationMap.id)\
        .join(ApplicationTypes, CouncilApplicationMap.application_id == ApplicationTypes.id)\
        .join(CouncilDetails, CouncilApplicationMap.council_id == CouncilDetails.council_id)
    
    if user_role == "admin": #admin
        applications = base_query.filter(UserApplicationDetails.stature.notin_((1,2,3))).all()
        return {
            "applications": [{
                "id": app.UserApplicationDetails.id,
                "application_name": app.UserApplicationDetails.application_name,
                "site_address": app.UserApplicationDetails.site_address,
                "site_post_code": app.UserApplicationDetails.site_post_code,
                "status_id": app.UserApplicationDetails.stature,
                "status": app.UserApplicationStatus.admin_status,
                "application_type": app.ApplicationTypes.kind,
                "council": app.CouncilDetails.council_name,
                "validation_timestamp": app.UserApplicationDetails.validation_timestamp,
                "approval_timestamp": app.UserApplicationDetails.approval_timestamp
            } for app in applications]
        }
    elif user_role == "planner": #planner
        applications = base_query.filter(UserApplicationDetails.stature.notin_((1,2,3,4,6))).all()
        return {
            "applications": [{
                "id": app.UserApplicationDetails.id,
                "application_name": app.UserApplicationDetails.application_name,
                "site_address": app.UserApplicationDetails.site_address,
                "site_post_code": app.UserApplicationDetails.site_post_code,
                "status_id": app.UserApplicationDetails.stature,
                "status": app.UserApplicationStatus.planner_status,
                "application_type": app.ApplicationTypes.kind,
                "council": app.CouncilDetails.council_name,
                "approval_timestamp": app.UserApplicationDetails.approval_timestamp
            } for app in applications]
        }
    else:
        return {"you are not authorized to fetch all applications"}

@app.post("/evaluate_application")
def council_comments(request: CommentRequest, authorization: Optional[str] = Header(None), db: Session = Depends(get_db)):
    user_id = verify_token(authorization, db)
    user_role = db.query(Users.role).filter(Users.id == user_id).scalar()

    admin_user_ids = get_admin_user_ids(db)
 
    if not user_role:
        raise HTTPException(status_code=401, detail="Role not found")
    
    # Check if the user_application_id and field_id already exist
    existing_record = db.query(CouncilComments).filter(
        CouncilComments.user_application_id == request.user_application_id,
        CouncilComments.user_id == user_id
    ).first()

    application = db.query(UserApplicationDetails).filter(
        UserApplicationDetails.id == request.user_application_id
    ).first()
    if not application:
        raise HTTPException(status_code=404, detail="Application not found")
    
    user_email = db.query(Users.email).filter(Users.id == application.user_id).scalar()
    if not user_email:
        raise HTTPException(status_code=404, detail="User email not found")

    def delete_existing_certificate(file_key):
        """Delete existing certificate from S3 if it exists"""
        if file_key:
            try:
                s3_client.delete_object(Bucket=S3_BUCKET_NAME, Key=file_key)
                print(f"Deleted existing certificate: {file_key}")
            except ClientError as e:
                print(f"Error deleting existing certificate {file_key}: {e}")
                # Continue even if deletion fails
    
    if user_role == "admin":
        if request.flag is None:
            if existing_record:
                # Parse existing comments as JSON list, or create empty list if it's a string
                try:
                    existing_comments = json.loads(existing_record.comments) if existing_record.comments else []
                except (json.JSONDecodeError, TypeError):
                    # If it's not valid JSON, treat it as a single comment and convert to list
                    existing_comments = [existing_record.comments] if existing_record.comments else []
                
                # Append new comment
                existing_comments.append(str(request.comments))
                
                # Save back as JSON string
                existing_record.comments = json.dumps(existing_comments)
                db.commit()
                return {"message":"comments updated successfully"}
            else:
                new_comment = CouncilComments(
                    user_application_id=request.user_application_id,
                    user_id=user_id,
                    comments=json.dumps([str(request.comments)]))
                db.add(new_comment)
                db.commit()
                return {"message":"comments added successfully"}
        elif request.flag:
            # Delete existing admin certificate if it exists
            delete_existing_certificate(application.admin_certificate)
            certificate_result = generate_certificate_for_application(request.user_application_id, user_role, request.flag, None if request.flag else request.comments)
            
            # Extract the values from the returned dictionary
            file_key = certificate_result["file_key"]
            presigned_url = certificate_result["presigned_url"]

            # Store the certificate file_key in the database
            application.admin_certificate = file_key

            send_application_status_email(user_email,user_role,request.flag,None if request.flag else request.comments,presigned_url)
            application.approval_timestamp = datetime.now(UTC)
            application.stature = 5
            db.commit()
            #------Trigger notification to applicant-----
            trigger_notification(
                db=db,
                user_id=application.user_id,
                notification_text="Your application has been validated by the admin.",
                status_id=5,
                user_application_id=application.id
            )
            return {"message":"application validated successfully", "Presigned_url":presigned_url}
        else:
            if existing_record:
                # Delete existing admin certificate if it exists
                delete_existing_certificate(application.admin_certificate)
                # Parse existing comments as JSON list, or create empty list if it's a string
                try:
                    existing_comments = json.loads(existing_record.comments) if existing_record.comments else []
                except (json.JSONDecodeError, TypeError):
                    # If it's not valid JSON, treat it as a single comment and convert to list
                    existing_comments = [existing_record.comments] if existing_record.comments else []
                # Make sure existing_comments is a list
                if not isinstance(existing_comments, list):
                    existing_comments = [existing_comments] if existing_comments else []
                # Append new comment
                existing_comments.append(str(request.comments))

                # Get all comments text BEFORE saving to database
                all_comments_text = "\n".join(existing_comments) if existing_comments else "No comments available"
                # Save back as JSON string
                existing_record.comments = json.dumps(existing_comments)
                application.stature = 6
                db.commit()

                certificate_result = generate_certificate_for_application(request.user_application_id, user_role, request.flag, None if request.flag else all_comments_text)
                
                # Extract the values from the returned dictionary
                file_key = certificate_result["file_key"]
                presigned_url = certificate_result["presigned_url"]
                
                # Store the certificate file_key in the database
                application.admin_certificate = file_key
                db.commit()

                send_application_status_email(user_email,user_role,request.flag,None if request.flag else all_comments_text,presigned_url)
                return {"message":"comments updated successfully", "Presigned_url":presigned_url}
            else:
                # Delete existing admin certificate if it exists
                delete_existing_certificate(application.admin_certificate)
                
                # Create new comment record
                new_comment = CouncilComments(
                    user_application_id=request.user_application_id,
                    user_id=user_id,
                    comments=json.dumps([str(request.comments)]))
                db.add(new_comment)
                application.stature = 6
                db.commit()
                
                # Use the new comment for certificate and email
                all_comments_text = str(request.comments)
                certificate_result = generate_certificate_for_application(request.user_application_id, user_role, request.flag, None if request.flag else all_comments_text)
                
                # Extract the values from the returned dictionary
                file_key = certificate_result["file_key"]
                presigned_url = certificate_result["presigned_url"]
                
                # Store the certificate file_key in the database
                application.admin_certificate = file_key
                db.commit()

                send_application_status_email(user_email,user_role,request.flag,None if request.flag else all_comments_text,presigned_url)
            # ------Trigger notification to applicant-----
            trigger_notification(
                db=db,
                user_id=application.user_id,
                notification_text="Your application requires attention. Please review admin comments.",
                status_id=6,
                user_application_id=application.id
            )
            return {"message":"comments added successfully", "Presigned_url":presigned_url}
            
    elif user_role == "planner":
        if request.flag is None:
            if existing_record:
                # Parse existing comments as JSON list, or create empty list if it's a string
                try:
                    existing_comments = json.loads(existing_record.comments) if existing_record.comments else []
                except (json.JSONDecodeError, TypeError):
                    # If it's not valid JSON, treat it as a single comment and convert to list
                    existing_comments = [existing_record.comments] if existing_record.comments else []
                # Make sure existing_comments is a list
                if not isinstance(existing_comments, list):
                    existing_comments = [existing_comments] if existing_comments else []
                # Append new comment
                existing_comments.append(str(request.comments))
                
                # Save back as JSON string
                existing_record.comments = json.dumps(existing_comments)
                db.commit()
                return {"message":"comments updated successfully"}
            else:
                new_comment = CouncilComments(
                    user_application_id=request.user_application_id,
                    user_id=user_id,
                    comments=json.dumps([str(request.comments)]))
                db.add(new_comment)
                db.commit()
                return {"message":"comments added successfully"}
        elif request.flag:
            # Delete existing admin certificate if it exists
            delete_existing_certificate(application.planner_certificate)
            certificate_result = generate_certificate_for_application(request.user_application_id, user_role, request.flag, None if request.flag else request.comments)
            
            # Extract the values from the returned dictionary
            file_key = certificate_result["file_key"]
            presigned_url = certificate_result["presigned_url"]
            
            # Store the certificate file_key in the database
            application.planner_certificate = file_key

            send_application_status_email(user_email,user_role,request.flag,None if request.flag else request.comments,presigned_url)
            application.stature = 9
            db.commit()
            #------Trigger notification to applicant-----
            trigger_notification(
                db=db,
                user_id=application.user_id,
                notification_text="Your application has been approved by the planner.",
                status_id=9,
                user_application_id=application.id
            )
            # Notify admin
            for admin_id in admin_user_ids:
                trigger_notification(
                    db=db,
                    user_id=admin_id,
                    notification_text=f"Application {application.id} has been approved by the planner.",
                    status_id=9,
                    user_application_id=application.id
                )
            return {"message":"application approved successfully", "Presigned_url":presigned_url}
        else:
            if existing_record:
                # Delete existing admin certificate if it exists
                delete_existing_certificate(application.planner_certificate)
                
                # Parse existing comments as JSON list, or create empty list if it's a string
                try:
                    existing_comments = json.loads(existing_record.comments) if existing_record.comments else []
                except (json.JSONDecodeError, TypeError):
                    # If it's not valid JSON, treat it as a single comment and convert to list
                    existing_comments = [existing_record.comments] if existing_record.comments else []
                
                # Append new comment
                existing_comments.append(str(request.comments))

                # Get all comments text BEFORE saving to database
                all_comments_text = "\n".join(existing_comments) if existing_comments else "No comments available"
                
                # Save back as JSON string
                existing_record.comments = json.dumps(existing_comments)
                application.stature = 10
                db.commit()

                certificate_result = generate_certificate_for_application(request.user_application_id, user_role, request.flag, None if request.flag else all_comments_text)
                
                # Extract the values from the returned dictionary
                file_key = certificate_result["file_key"]
                presigned_url = certificate_result["presigned_url"]
                
                # Store the certificate file_key in the database
                application.planner_certificate = file_key
                db.commit()

                send_application_status_email(user_email,user_role,request.flag,None if request.flag else all_comments_text,presigned_url)
                return {"message":"comments updated successfully", "Presigned_url":presigned_url}
            else:
                # Delete existing admin certificate if it exists
                delete_existing_certificate(application.planner_certificate)
                
                # Create new comment record
                new_comment = CouncilComments(
                    user_application_id=request.user_application_id,
                    user_id=user_id,
                    comments=json.dumps([str(request.comments)]))
                db.add(new_comment)
                application.stature = 10
                db.commit()
                
                # Use the new comment for certificate and email
                all_comments_text = str(request.comments)
                certificate_result = generate_certificate_for_application(request.user_application_id, user_role, request.flag, None if request.flag else all_comments_text)
                
                # Extract the values from the returned dictionary
                file_key = certificate_result["file_key"]
                presigned_url = certificate_result["presigned_url"]
                
                # Store the certificate file_key in the database
                application.planner_certificate = file_key
                db.commit()
                
                send_application_status_email(user_email,user_role,request.flag,None if request.flag else all_comments_text,presigned_url)
            # ------Trigger notification to applicant-----
            trigger_notification(
                db=db,
                user_id=application.user_id,
                notification_text="Your application has been refused by the planner. Please review comments.",
                status_id=10,
                user_application_id=application.id
            )
            # Notify all admins
            for admin_id in admin_user_ids:
                trigger_notification(
                    db=db,
                    user_id=admin_id,
                    notification_text=f"Application {application.id} has been refused by the planner.",
                    status_id=10,
                    user_application_id=application.id
                )
            return {"message":"comments added successfully", "Presigned_url":presigned_url}
    else:
        return {"you are not authorized to add comments"}

@app.get("/get_all_planner")
def get_all_planner(authorization: Optional[str] = Header(None), db: Session = Depends(get_db)):
    user_id = verify_token(authorization, db)
    user_role = db.query(Users.role).filter(Users.id == user_id).scalar()
    planner_stats = db.query(
            Users.id,
            Users.first_name,
            Users.last_name,
            Users.email,
            Users.created_at.label('Joined_date'),
            func.count(case((UserApplicationDetails.stature != 9, 1))).label('in_progress'),
            func.count(case((UserApplicationDetails.stature == 9, 1))).label('completed'),
        ).outerjoin(
            UserApplicationDetails, Users.id == UserApplicationDetails.assigned_to
        ).filter(
            Users.role == "planner"
        ).group_by(
            Users.id, Users.first_name, Users.last_name, Users.email, Users.created_at
        ).all()
    if user_role == "admin":
        return {
            "planners": [{
                "id": planner.id,
                "name": planner.first_name + " " + planner.last_name,
                "email": planner.email,
                "joined_date": planner.Joined_date,
                "in_progress": planner.in_progress,
                "completed": planner.completed
            } for planner in planner_stats]
        }
    else:
        return{"message": "you are not authorized to fetch all planners"}

@app.post("/assign_to_planner")
def assign_to_planner(request: AssignPlanner, authorization: Optional[str] = Header(None), db: Session = Depends(get_db)):
    user_id = verify_token(authorization, db)
    user_role = db.query(Users.role).filter(Users.id == user_id).scalar()
    if user_role == "admin":
        application = db.query(UserApplicationDetails).filter(
            UserApplicationDetails.id == request.user_application_id
        ).first()
        if not application:
            raise HTTPException(status_code=404, detail="Application not found")
        application.assigned_to = request.planner_id
        db.commit()
        #------Trigger notification to planner-----
        if application.assigned_to:
            trigger_notification(
                db=db,
                user_id=application.assigned_to,
                notification_text="A new application has been validated and assigned to you for review.",
                status_id=5,
                user_application_id=application.id
            )
        return {"message":"planner assigned successfully"}
    else:
        return {"message":"you are not authorized to assign a planner"}
    

@app.get("/notifications")
def get_notifications(
    authorization: Optional[str] = Header(None),
    application_id: Optional[int] = Query(None),  # <-- Add this line
    db: Session = Depends(get_db)
):
    user_id = verify_token(authorization, db)

    query = db.query(Notification).filter(Notification.user_id == user_id)
    if application_id is not None:
        query = query.filter(Notification.user_application_id == application_id)
    notifications = query.order_by(Notification.created_at.desc()).all()

    return [
        {
            "id": n.id,
            "text": n.notification_text,
            "status_id": n.status_id,
            "application_id": n.user_application_id,
            "read": n.read_status,
            "created_at": n.created_at
        } for n in notifications
    ]


@app.post("/notifications/mark-read")
def mark_notification_read(notification_id: int = Form(...), authorization: Optional[str] = Header(None), db: Session = Depends(get_db)):
    user_id = verify_token(authorization, db)
 
    notif = db.query(Notification)\
        .filter(Notification.id == notification_id, Notification.user_id == user_id)\
        .first()
 
    if not notif:
        raise HTTPException(status_code=404, detail="Notification not found")
 
    notif.read_status = True
    db.commit()
 
    return {"message": "Notification marked as read."}

@app.post("/submit_application")
async def upload_signature(
    user_application_id: int = Form(...),
    esign_ss: Optional[UploadFile] = File(None, description="PNG files to upload"),
    text_sign: Optional[str] = Form(None),
    authorization: Optional[str] = Header(None), 
    db: Session = Depends(get_db)
):
    user_id = verify_token(authorization, db)
    user_app = db.query(UserApplicationDetails).filter_by(id=user_application_id, user_id=user_id).first()
    if not user_app:
        raise HTTPException(status_code=403, detail="Unauthorized or application not found.")

    def delete_existing_signature(file_key):
        """Delete existing signature from S3 if it exists"""
        if file_key:
            try:
                s3_client.delete_object(Bucket=S3_BUCKET_NAME, Key=file_key)
                print(f"Deleted existing certificate: {file_key}")
            except ClientError as e:
                print(f"Error deleting existing certificate {file_key}: {e}")
                # Continue even if deletion fails
    if esign_ss:
        # Validate file type and extension for PNG
        if not esign_ss.content_type or not esign_ss.content_type.startswith('image/png'):
            raise HTTPException(status_code=400, detail="Only PNG files are allowed for signature upload.")
        
        if not esign_ss.filename or not esign_ss.filename.lower().endswith('.png'):
            raise HTTPException(status_code=400, detail="File must have .png extension.")
        
        try:
            # Generate file key using the same pattern as upload_documents
            delete_existing_signature(user_app.signature)
            file_content = await esign_ss.read()
            file_key = f"signatures/{uuid.uuid4()}_signatures_{esign_ss.filename}"
            # Use the exact same S3 upload code as your upload_documents API
            s3_client.put_object(
                Bucket=S3_BUCKET_NAME,
                Key=file_key,
                Body=file_content,
                ContentType='image/png',
                ACL="public-read"
            )
            user_app.signature = file_key
            user_app.stature = 4
            user_app.validation_timestamp = datetime.now(UTC)

            # Trigger
            trigger_notification(
            db=db,
            user_id=user_id,
            notification_text="Your application has been submitted for validation.",
            status_id=4,
            user_application_id=user_application_id
            )
            db.commit()
            return {"message": "Your application has been submitted for validation.", "signature": file_key}
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Error uploading signature: {str(e)}")
        finally:
            await esign_ss.close()
    elif text_sign:
        delete_existing_signature(user_app.signature)
        user_app.signature = text_sign
        user_app.stature = 4
        user_app.validation_timestamp = datetime.now(UTC)

        # Trigger
        trigger_notification(
        db=db,
        user_id=user_id,
        notification_text="Your application has been submitted for validation.",
        status_id=4,
        user_application_id=user_application_id
        )
        db.commit()
        return {"message": "Your application has been submitted for validation.", "signature": text_sign}
    else:
        raise HTTPException(status_code=400, detail="Either a PNG file (esign_ss) or text signature (text_sign) must be provided.")