#!/bin/bash

# Set up environment for GPU/cuDNN support
export CUDA_VISIBLE_DEVICES=0
export LD_LIBRARY_PATH=/usr/local/cuda/lib64:/usr/local/lib:$LD_LIBRARY_PATH

# Navigate to project directory
cd /home/<USER>/Paive-Backend/PlanningPal-Backend

# Activate virtual environment
source venv/bin/activate

# Verify GPU is available
echo "Checking GPU availability..."
if ! nvidia-smi | grep "Tesla T4" > /dev/null; then
    echo "GPU is off, attempting to start GPU..."
    # Add commands here to start the GPU if needed
    # This might involve cloud provider specific commands
    sudo nvidia-smi -pm 1
    sudo nvidia-smi -e 0
fi

nvidia-smi | grep "Tesla T4"

# Verify cuDNN libraries are accessible
echo "Checking cuDNN libraries..."
ls -la /usr/local/cuda/lib64/libcudnn.so

# Start the API
echo "Starting API with GPU support..."
nohup python3 main.py --server.port 8000 2>&1 | awk '{print strftime("[%Y-%m-%d %H:%M:%S]"), $0; fflush()}' > BackendAPI.log &
