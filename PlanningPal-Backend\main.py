import os
os.environ["KMP_DUPLICATE_LIB_OK"] = "TRUE"

from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from fastapi import FastAPI
import logging
from dotenv import load_dotenv

# 1) Import the three sub-apps
from authentication_api.app import app as auth_app
from application_api.app import app as application_app
from chatbot_api.chatbot_api import app as chatbot_app

load_dotenv()
origins = os.getenv("ALLOWED_ORIGINS")
allow_origins = [origin.strip() for origin in origins.split(",") if origin.strip()]

# 2) Create a single FastAPI application
app = FastAPI(
    title="Unified PlanningPal Backend",
    description="All authentication, application, and chatbot APIs in one server",
    version="1.0.0"
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=allow_origins,
    allow_credentials=True,
    allow_methods=["*"],  # Allow all HTTP methods
    allow_headers=["*"],  # Allow all headers
)

# 3) Mount the sub-apps on different paths
app.mount("/auth", auth_app)
app.mount("/application", application_app)
app.mount("/chatbot", chatbot_app)

# 4) If you run main.py directly with `python main.py`
if __name__ == "__main__":
    # Get host and port from environment variables with secure defaults
    HOST = os.getenv("HOST")  # Default to localhost
    PORT = int(os.getenv("PORT"))   # Default to port 8000
    uvicorn.run("main:app", host=HOST, port=PORT, reload=True ) #False, workers=5, loop="asyncio")
